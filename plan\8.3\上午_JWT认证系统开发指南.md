
# Day 3 (8月3日) 上午 - JWT认证系统开发指南
**学习时间：** 3小时 (9:00-12:00)
**目标**: 实现企业级用户认证系统

## 📖 理论学习阶段 (1小时)

### 必读文档
1. **JWT Token原理** (30分钟)
   - 阅读: [JWT官方介绍](https://jwt.io/introduction/)
   - 重点关注: Token结构、签名验证、安全性原理

2. **FastAPI认证系统** (30分钟)
   - 阅读: [FastAPI安全性文档](https://fastapi.tiangolo.com/tutorial/security/)
   - 重点: OAuth2、依赖注入、中间件使用

### 🎥 推荐视频 (30分钟)
- **B站视频**: "JWT认证原理详解" by 程序员鱼皮
- **重点观看**: JWT工作流程和安全实践

## 🛠️ 实践开发阶段 (2小时)

### 开发任务1: JWT工具类开发 (30分钟)
```python
# 创建文件: app/utils/jwt_utils.py
# 任务:
# 1. 实现JWT Token生成函数
# 2. 实现Token验证和解析
# 3. 实现Token刷新机制
# 4. 添加过期时间处理逻辑
```

**核心功能**:
- Token编码/解码
- 签名验证
- 过期检查
- 用户信息提取

### 开发任务2: 认证中间件开发 (45分钟)
```python
# 创建文件: app/middleware/auth.py
# 任务:
# 1. 创建认证依赖函数
# 2. 实现权限检查装饰器
# 3. 添加用户角色验证
# 4. 处理认证异常和错误响应
```

**核心功能**:
- 请求拦截
- Token提取
- 用户身份验证
- 权限级别控制

### 开发任务3: 用户管理API开发 (45分钟)
```python
# 创建文件: app/api/auth.py
# 任务:
# 1. 实现用户注册接口
# 2. 实现用户登录接口
# 3. 实现Token刷新接口
# 4. 添加用户信息查询接口
```

**API端点设计**:
- POST /auth/register - 用户注册
- POST /auth/login - 用户登录
- POST /auth/refresh - Token刷新
- GET /auth/me - 获取当前用户信息

## 📝 学习笔记
**创建文件**: `day3_morning_notes.md`
**记录内容**:
- JWT认证原理和优势
- FastAPI认证系统架构
- 安全最佳实践
- 常见安全漏洞防范

## ✅ 上午检查清单
- [ ] 理解JWT认证工作原理
- [ ] 完成JWT工具类开发
- [ ] 实现认证中间件
- [ ] 完成用户管理API
- [ ] 测试认证流程完整性

## 🔄 下午预习
- 准备学习反爬虫对抗技术
- 了解常见反爬虫机制
- 思考如何突破IP限制和验证码

---

## 💡 实战提示

### JWT最佳实践
1. **Token安全存储**
   - 前端使用HttpOnly Cookie
   - 避免存储在localStorage
   - 设置合理的过期时间

2. **签名密钥管理**
   - 使用强随机密钥
   - 定期轮换密钥
   - 环境变量存储

3. **权限设计**
   - 最小权限原则
   - 角色基础访问控制(RBAC)
   - 细粒度权限控制

### 常见安全问题
- Token泄露风险
- 重放攻击防护
- 跨站请求伪造(CSRF)
- 会话固定攻击

### 调试技巧
- 使用jwt.io在线解析Token
- 设置详细的日志记录
- 单元测试覆盖认证流程

# 最简单的JWT示例
import jwt
from datetime import datetime, timedelta

# 1. 创建Token
def create_token(user_id: int):
    payload = {
        "user_id": user_id,
        "exp": datetime.utcnow() + timedelta(hours=1)
    }
    token = jwt.encode(payload, "secret_key", algorithm="HS256")
    return token

# 2. 验证Token
def verify_token(token: str):
    try:
        payload = jwt.decode(token, "secret_key", algorithms=["HS256"])
        return payload["user_id"]
    except jwt.ExpiredSignatureError:
        return None

# 3. 测试
token = create_token(123)
print(f"Token: {token}")
user_id = verify_token(token)
print(f"User ID: {user_id}")


