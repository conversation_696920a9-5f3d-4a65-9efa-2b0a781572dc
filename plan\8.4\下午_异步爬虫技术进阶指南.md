# ⚡ 异步爬虫技术进阶指南 (下午专用)

## 🎯 学习目标：掌握高性能异步爬虫技术

### 📚 第一步：理解异步编程的本质 (20分钟)

**什么是异步编程？**
- 异步编程 = 非阻塞式编程，允许程序在等待I/O操作时执行其他任务
- 核心概念：协程(Coroutine)、事件循环(Event Loop)、并发(Concurrency)
- 优势：高并发、低资源消耗、更好的响应性

**同步 vs 异步对比**：
```python
# 同步爬虫（阻塞式）
import requests
import time

def sync_crawler():
    urls = ['http://example.com'] * 100
    start_time = time.time()
    
    for url in urls:
        response = requests.get(url)  # 阻塞等待
        print(f"获取 {url}: {response.status_code}")
    
    print(f"同步爬虫耗时: {time.time() - start_time:.2f}s")

# 异步爬虫（非阻塞式）
import asyncio
import aiohttp

async def async_crawler():
    urls = ['http://example.com'] * 100
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        for url in urls:
            task = fetch_url(session, url)  # 创建协程任务
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)  # 并发执行
    
    print(f"异步爬虫耗时: {time.time() - start_time:.2f}s")

async def fetch_url(session, url):
    async with session.get(url) as response:
        return await response.text()
```

**异步编程核心概念**：
- **协程(Coroutine)**: 可以暂停和恢复的函数
- **事件循环(Event Loop)**: 管理和执行协程的调度器
- **任务(Task)**: 被调度执行的协程
- **Future**: 代表异步操作的结果

---

## 🎥 第二步：视频学习资源 (90分钟)

### 推荐视频学习路径

#### 核心视频1：Python异步编程基础 (30分钟)
**B站推荐**：
- 🎬 "Python异步编程从入门到实战" by 程序员鱼皮
- 🎬 "asyncio事件循环深度解析" by 黑马程序员
- 🎬 "协程和生成器的区别" by 尚硅谷

**学习重点**：
- async/await语法详解
- 事件循环的工作原理
- 协程的创建和执行
- 异步上下文管理器

#### 核心视频2：aiohttp异步HTTP库 (30分钟)
**B站推荐**：
- 🎬 "aiohttp异步HTTP客户端实战" by 千锋教育
- 🎬 "异步爬虫性能优化技巧" by 老男孩教育
- 🎬 "aiohttp会话管理和连接池" by 马士兵教育

**学习重点**：
- aiohttp客户端使用
- 异步会话管理
- 连接池配置优化
- 异常处理和重试机制

#### 核心视频3：高并发爬虫架构 (30分钟)
**B站推荐**：
- 🎬 "分布式爬虫架构设计" by 咕泡学院
- 🎬 "异步爬虫并发控制策略" by 图灵学院
- 🎬 "爬虫性能监控和调优" by 动力节点

**学习重点**：
- 并发控制和限流策略
- 异步队列和任务调度
- 性能监控和指标收集
- 分布式爬虫设计

### 视频学习笔记模板
```markdown
## 视频学习笔记 - 异步爬虫技术

### 视频1：Python异步编程基础
**时长**：XX分钟
**关键概念**：
- [ ] async/await语法
- [ ] 事件循环机制
- [ ] 协程生命周期
- [ ] 异步上下文管理

**重要代码片段**：
```python
# 记录视频中的重要代码示例
```

**疑问和思考**：
- 问题1：
- 问题2：

### 视频2：aiohttp异步HTTP库
**时长**：XX分钟
**关键概念**：
- [ ] ClientSession使用
- [ ] 连接池配置
- [ ] 异步请求处理
- [ ] 错误处理机制

**实践要点**：
- 要点1：
- 要点2：
```

---

## 🛠️ 第三步：异步环境搭建 (15分钟)

### 安装异步相关依赖
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows

# 安装异步HTTP库
pip install aiohttp[speedups]    # 异步HTTP客户端（包含性能优化）
pip install aiofiles             # 异步文件操作
pip install asyncio-mqtt         # 异步MQTT客户端（可选）

# 安装异步数据库驱动
pip install asyncpg              # PostgreSQL异步驱动
pip install aiomysql             # MySQL异步驱动
pip install aiosqlite            # SQLite异步驱动

# 性能监控和调试工具
pip install aiomonitor           # 异步程序监控
pip install aiodebug             # 异步调试工具
pip install uvloop               # 高性能事件循环（Linux/Mac）

# 验证安装
python -c "import aiohttp; print(f'aiohttp版本: {aiohttp.__version__}')"
python -c "import asyncio; print('asyncio可用')"
```

### 项目结构设计
```
async_spider/
├── __init__.py
├── core/
│   ├── __init__.py
│   ├── spider.py          # 异步爬虫核心
│   ├── session.py         # 异步会话管理
│   ├── queue.py           # 异步任务队列
│   └── scheduler.py       # 任务调度器
├── utils/
│   ├── __init__.py
│   ├── rate_limiter.py    # 限流器
│   ├── retry.py           # 重试机制
│   └── monitor.py         # 性能监控
├── storage/
│   ├── __init__.py
│   ├── async_db.py        # 异步数据库操作
│   └── async_file.py      # 异步文件操作
└── examples/
    ├── basic_spider.py
    ├── advanced_spider.py
    └── performance_test.py
```

---

## ⚡ 第四步：异步HTTP请求基础 (45分钟)

### 异步HTTP客户端封装
```python
# 文件：async_spider/core/session.py
import asyncio
import aiohttp
import time
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

@dataclass
class RequestConfig:
    """请求配置"""
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    headers: Optional[Dict[str, str]] = None
    proxy: Optional[str] = None
    verify_ssl: bool = True

class AsyncHttpClient:
    """异步HTTP客户端"""
    
    def __init__(self, config: RequestConfig = None):
        self.config = config or RequestConfig()
        self.session: Optional[aiohttp.ClientSession] = None
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_time': 0.0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self):
        """启动HTTP客户端"""
        # 配置连接器
        connector = aiohttp.TCPConnector(
            limit=100,              # 总连接池大小
            limit_per_host=30,      # 每个主机的连接数
            ttl_dns_cache=300,      # DNS缓存时间
            use_dns_cache=True,     # 启用DNS缓存
            ssl=self.config.verify_ssl
        )
        
        # 配置超时
        timeout = aiohttp.ClientTimeout(
            total=self.config.timeout,
            connect=10,
            sock_read=10
        )
        
        # 创建会话
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self.config.headers or {},
            trust_env=True  # 信任环境变量中的代理设置
        )
        
        logger.info("异步HTTP客户端已启动")
    
    async def close(self):
        """关闭HTTP客户端"""
        if self.session:
            await self.session.close()
            logger.info("异步HTTP客户端已关闭")
    
    async def request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Optional[aiohttp.ClientResponse]:
        """发送异步HTTP请求"""
        if not self.session:
            raise RuntimeError("HTTP客户端未启动，请先调用start()方法")
        
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        for attempt in range(self.config.max_retries + 1):
            try:
                # 添加代理配置
                if self.config.proxy:
                    kwargs['proxy'] = self.config.proxy
                
                async with self.session.request(method, url, **kwargs) as response:
                    # 记录统计信息
                    request_time = time.time() - start_time
                    self.stats['total_time'] += request_time
                    
                    if response.status < 400:
                        self.stats['successful_requests'] += 1
                        logger.debug(f"请求成功: {method} {url} ({response.status})")
                    else:
                        logger.warning(f"请求失败: {method} {url} ({response.status})")
                    
                    return response
                    
            except asyncio.TimeoutError:
                logger.warning(f"请求超时 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {url}")
            except aiohttp.ClientError as e:
                logger.warning(f"请求错误 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {url} - {e}")
            except Exception as e:
                logger.error(f"未知错误 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {url} - {e}")
            
            # 重试延迟
            if attempt < self.config.max_retries:
                await asyncio.sleep(self.config.retry_delay * (attempt + 1))
        
        # 所有重试都失败
        self.stats['failed_requests'] += 1
        logger.error(f"请求最终失败: {method} {url}")
        return None
    
    async def get(self, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """GET请求"""
        return await self.request('GET', url, **kwargs)
    
    async def post(self, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """POST请求"""
        return await self.request('POST', url, **kwargs)
    
    async def fetch_text(self, url: str, **kwargs) -> Optional[str]:
        """获取文本内容"""
        response = await self.get(url, **kwargs)
        if response:
            try:
                return await response.text()
            except Exception as e:
                logger.error(f"读取响应文本失败: {url} - {e}")
        return None
    
    async def fetch_json(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """获取JSON内容"""
        response = await self.get(url, **kwargs)
        if response:
            try:
                return await response.json()
            except Exception as e:
                logger.error(f"解析JSON失败: {url} - {e}")
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = self.stats['total_requests']
        if total_requests == 0:
            return self.stats
        
        return {
            **self.stats,
            'success_rate': self.stats['successful_requests'] / total_requests,
            'failure_rate': self.stats['failed_requests'] / total_requests,
            'avg_response_time': self.stats['total_time'] / total_requests
        }

# 测试异步HTTP客户端
async def test_async_http_client():
    """测试异步HTTP客户端"""
    print("=== 异步HTTP客户端测试 ===")
    
    config = RequestConfig(
        timeout=10,
        max_retries=2,
        headers={'User-Agent': 'AsyncSpider/1.0'}
    )
    
    async with AsyncHttpClient(config) as client:
        # 测试单个请求
        print("\n1. 测试单个请求")
        response = await client.get('http://httpbin.org/ip')
        if response:
            content = await response.text()
            print(f"响应状态: {response.status}")
            print(f"响应内容: {content[:100]}...")
        
        # 测试并发请求
        print("\n2. 测试并发请求")
        urls = [
            'http://httpbin.org/delay/1',
            'http://httpbin.org/delay/2',
            'http://httpbin.org/delay/1'
        ]
        
        start_time = time.time()
        tasks = [client.fetch_json(url) for url in urls]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        print(f"并发请求完成，耗时: {end_time - start_time:.2f}s")
        print(f"成功获取 {sum(1 for r in results if r)} 个响应")
        
        # 显示统计信息
        print("\n3. 统计信息")
        stats = client.get_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(test_async_http_client())
```

---

## 🕷️ 第五步：异步爬虫核心实现 (75分钟)

### 并发控制和限流器
```python
# 文件：async_spider/utils/rate_limiter.py
import asyncio
import time
from typing import Dict, Optional
from dataclasses import dataclass

@dataclass
class RateLimitConfig:
    """限流配置"""
    requests_per_second: float = 10.0
    burst_size: int = 20
    per_host_limit: float = 5.0

class TokenBucketLimiter:
    """令牌桶限流器"""

    def __init__(self, rate: float, burst_size: int):
        self.rate = rate  # 每秒生成的令牌数
        self.burst_size = burst_size  # 桶的容量
        self.tokens = burst_size  # 当前令牌数
        self.last_update = time.time()
        self.lock = asyncio.Lock()

    async def acquire(self) -> bool:
        """获取令牌"""
        async with self.lock:
            now = time.time()
            # 计算应该添加的令牌数
            elapsed = now - self.last_update
            tokens_to_add = elapsed * self.rate

            # 更新令牌数（不超过桶容量）
            self.tokens = min(self.burst_size, self.tokens + tokens_to_add)
            self.last_update = now

            # 检查是否有可用令牌
            if self.tokens >= 1:
                self.tokens -= 1
                return True
            return False

    async def wait_for_token(self):
        """等待获取令牌"""
        while not await self.acquire():
            # 计算需要等待的时间
            wait_time = 1.0 / self.rate
            await asyncio.sleep(wait_time)

class AsyncRateLimiter:
    """异步限流器"""

    def __init__(self, config: RateLimitConfig):
        self.config = config
        self.global_limiter = TokenBucketLimiter(
            config.requests_per_second,
            config.burst_size
        )
        self.host_limiters: Dict[str, TokenBucketLimiter] = {}
        self.semaphore = asyncio.Semaphore(config.burst_size)

    def _get_host_limiter(self, host: str) -> TokenBucketLimiter:
        """获取主机限流器"""
        if host not in self.host_limiters:
            self.host_limiters[host] = TokenBucketLimiter(
                self.config.per_host_limit,
                max(5, int(self.config.per_host_limit * 2))
            )
        return self.host_limiters[host]

    async def acquire(self, url: str):
        """获取请求许可"""
        from urllib.parse import urlparse
        host = urlparse(url).netloc

        # 全局限流
        await self.global_limiter.wait_for_token()

        # 主机限流
        host_limiter = self._get_host_limiter(host)
        await host_limiter.wait_for_token()

        # 并发数限制
        await self.semaphore.acquire()

    def release(self):
        """释放许可"""
        self.semaphore.release()

# 异步爬虫核心
# 文件：async_spider/core/spider.py
import asyncio
import aiohttp
import time
import logging
from typing import List, Dict, Any, Optional, Callable, AsyncGenerator
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass, field
from bs4 import BeautifulSoup

from async_spider.core.session import AsyncHttpClient, RequestConfig
from async_spider.utils.rate_limiter import AsyncRateLimiter, RateLimitConfig

logger = logging.getLogger(__name__)

@dataclass
class SpiderConfig:
    """爬虫配置"""
    max_concurrent: int = 50
    request_delay: float = 0.1
    max_retries: int = 3
    timeout: int = 30
    rate_limit: RateLimitConfig = field(default_factory=RateLimitConfig)
    headers: Dict[str, str] = field(default_factory=dict)
    follow_redirects: bool = True
    max_depth: int = 3

@dataclass
class SpiderResult:
    """爬虫结果"""
    url: str
    status_code: Optional[int] = None
    content: Optional[str] = None
    extracted_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    response_time: float = 0.0
    depth: int = 0

class AsyncSpider:
    """异步爬虫"""

    def __init__(self, config: SpiderConfig = None):
        self.config = config or SpiderConfig()
        self.rate_limiter = AsyncRateLimiter(self.config.rate_limit)
        self.http_client: Optional[AsyncHttpClient] = None
        self.results: List[SpiderResult] = []
        self.visited_urls: set = set()
        self.stats = {
            'total_urls': 0,
            'successful_urls': 0,
            'failed_urls': 0,
            'start_time': None,
            'end_time': None
        }

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()

    async def start(self):
        """启动爬虫"""
        request_config = RequestConfig(
            timeout=self.config.timeout,
            max_retries=self.config.max_retries,
            headers=self.config.headers
        )
        self.http_client = AsyncHttpClient(request_config)
        await self.http_client.start()
        self.stats['start_time'] = time.time()
        logger.info("异步爬虫已启动")

    async def stop(self):
        """停止爬虫"""
        if self.http_client:
            await self.http_client.close()
        self.stats['end_time'] = time.time()
        logger.info("异步爬虫已停止")

    async def fetch_url(self, url: str, depth: int = 0) -> SpiderResult:
        """获取单个URL"""
        if url in self.visited_urls:
            return SpiderResult(url=url, error="URL已访问")

        self.visited_urls.add(url)
        result = SpiderResult(url=url, depth=depth)

        try:
            # 限流控制
            await self.rate_limiter.acquire(url)

            start_time = time.time()
            response = await self.http_client.get(url)
            result.response_time = time.time() - start_time

            if response:
                result.status_code = response.status
                if response.status == 200:
                    result.content = await response.text()
                    self.stats['successful_urls'] += 1
                else:
                    result.error = f"HTTP {response.status}"
                    self.stats['failed_urls'] += 1
            else:
                result.error = "请求失败"
                self.stats['failed_urls'] += 1

        except Exception as e:
            result.error = str(e)
            self.stats['failed_urls'] += 1
            logger.error(f"获取URL失败: {url} - {e}")
        finally:
            self.rate_limiter.release()
            self.stats['total_urls'] += 1

        return result

    async def extract_data(self, result: SpiderResult, extractor: Callable = None) -> SpiderResult:
        """提取数据"""
        if not result.content or result.error:
            return result

        try:
            if extractor:
                # 使用自定义提取器
                result.extracted_data = await extractor(result.content, result.url)
            else:
                # 默认提取器
                result.extracted_data = await self._default_extractor(result.content, result.url)
        except Exception as e:
            logger.error(f"数据提取失败: {result.url} - {e}")
            result.error = f"数据提取失败: {e}"

        return result

    async def _default_extractor(self, content: str, url: str) -> Dict[str, Any]:
        """默认数据提取器"""
        soup = BeautifulSoup(content, 'html.parser')

        # 提取基础信息
        title = soup.find('title')
        title_text = title.text.strip() if title else ""

        # 提取链接
        links = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            absolute_url = urljoin(url, href)
            links.append({
                'text': link.text.strip(),
                'url': absolute_url
            })

        # 提取图片
        images = []
        for img in soup.find_all('img', src=True):
            src = img['src']
            absolute_url = urljoin(url, src)
            images.append({
                'alt': img.get('alt', ''),
                'url': absolute_url
            })

        return {
            'title': title_text,
            'links': links[:20],  # 限制链接数量
            'images': images[:10],  # 限制图片数量
            'text_length': len(soup.get_text()),
            'link_count': len(links),
            'image_count': len(images)
        }

    async def crawl_urls(
        self,
        urls: List[str],
        extractor: Callable = None,
        max_concurrent: int = None
    ) -> List[SpiderResult]:
        """爬取URL列表"""
        if not self.http_client:
            raise RuntimeError("爬虫未启动，请先调用start()方法")

        max_concurrent = max_concurrent or self.config.max_concurrent
        semaphore = asyncio.Semaphore(max_concurrent)

        async def crawl_single_url(url: str) -> SpiderResult:
            async with semaphore:
                # 获取内容
                result = await self.fetch_url(url)
                # 提取数据
                result = await self.extract_data(result, extractor)
                return result

        # 创建任务
        tasks = [crawl_single_url(url) for url in urls]

        # 执行任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"任务执行异常: {result}")
                processed_results.append(SpiderResult(url="unknown", error=str(result)))
            else:
                processed_results.append(result)

        self.results.extend(processed_results)
        return processed_results

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()

        if stats['start_time'] and stats['end_time']:
            stats['duration'] = stats['end_time'] - stats['start_time']
            stats['urls_per_second'] = stats['total_urls'] / stats['duration']

        if stats['total_urls'] > 0:
            stats['success_rate'] = stats['successful_urls'] / stats['total_urls']
            stats['failure_rate'] = stats['failed_urls'] / stats['total_urls']

        return stats

# 测试异步爬虫
async def test_async_spider():
    """测试异步爬虫"""
    print("=== 异步爬虫测试 ===")

    # 配置爬虫
    config = SpiderConfig(
        max_concurrent=10,
        request_delay=0.5,
        rate_limit=RateLimitConfig(requests_per_second=5.0)
    )

    # 测试URL列表
    test_urls = [
        'http://httpbin.org/delay/1',
        'http://httpbin.org/delay/2',
        'http://httpbin.org/json',
        'http://httpbin.org/html',
        'http://httpbin.org/xml'
    ]

    async with AsyncSpider(config) as spider:
        print(f"\n开始爬取 {len(test_urls)} 个URL...")

        start_time = time.time()
        results = await spider.crawl_urls(test_urls)
        end_time = time.time()

        print(f"爬取完成，耗时: {end_time - start_time:.2f}s")

        # 显示结果
        print(f"\n=== 爬取结果 ===")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.url}")
            print(f"   状态: {result.status_code}")
            print(f"   响应时间: {result.response_time:.3f}s")
            if result.error:
                print(f"   错误: {result.error}")
            elif result.extracted_data:
                print(f"   标题: {result.extracted_data.get('title', 'N/A')[:50]}...")

        # 显示统计信息
        print(f"\n=== 统计信息 ===")
        stats = spider.get_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(test_async_spider())
```

---

## 💾 第六步：异步数据存储 (45分钟)

### 异步数据库操作
```python
# 文件：async_spider/storage/async_db.py
import asyncio
import aiosqlite
import json
import time
from typing import List, Dict, Any, Optional
from dataclasses import asdict
from datetime import datetime

from async_spider.core.spider import SpiderResult

class AsyncDatabaseStorage:
    """异步数据库存储"""

    def __init__(self, db_path: str = "async_spider.db"):
        self.db_path = db_path
        self.connection: Optional[aiosqlite.Connection] = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def connect(self):
        """连接数据库"""
        self.connection = await aiosqlite.connect(self.db_path)
        await self.create_tables()

    async def close(self):
        """关闭数据库连接"""
        if self.connection:
            await self.connection.close()

    async def create_tables(self):
        """创建数据表"""
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS spider_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT NOT NULL,
                status_code INTEGER,
                content TEXT,
                extracted_data TEXT,
                error TEXT,
                response_time REAL,
                depth INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_url ON spider_results(url)
        """)

        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_status_code ON spider_results(status_code)
        """)

        await self.connection.commit()

    async def save_result(self, result: SpiderResult) -> int:
        """保存单个结果"""
        extracted_data_json = json.dumps(result.extracted_data) if result.extracted_data else None

        cursor = await self.connection.execute("""
            INSERT INTO spider_results
            (url, status_code, content, extracted_data, error, response_time, depth)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            result.url,
            result.status_code,
            result.content,
            extracted_data_json,
            result.error,
            result.response_time,
            result.depth
        ))

        await self.connection.commit()
        return cursor.lastrowid

    async def save_results_batch(self, results: List[SpiderResult]) -> List[int]:
        """批量保存结果"""
        data = []
        for result in results:
            extracted_data_json = json.dumps(result.extracted_data) if result.extracted_data else None
            data.append((
                result.url,
                result.status_code,
                result.content,
                extracted_data_json,
                result.error,
                result.response_time,
                result.depth
            ))

        await self.connection.executemany("""
            INSERT INTO spider_results
            (url, status_code, content, extracted_data, error, response_time, depth)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, data)

        await self.connection.commit()
        return list(range(len(results)))  # 简化的ID列表

    async def get_results(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取结果列表"""
        cursor = await self.connection.execute("""
            SELECT * FROM spider_results
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """, (limit, offset))

        rows = await cursor.fetchall()
        columns = [description[0] for description in cursor.description]

        results = []
        for row in rows:
            result_dict = dict(zip(columns, row))
            # 解析JSON数据
            if result_dict['extracted_data']:
                try:
                    result_dict['extracted_data'] = json.loads(result_dict['extracted_data'])
                except json.JSONDecodeError:
                    result_dict['extracted_data'] = None
            results.append(result_dict)

        return results

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        # 总数统计
        cursor = await self.connection.execute("SELECT COUNT(*) FROM spider_results")
        total_count = (await cursor.fetchone())[0]

        # 成功率统计
        cursor = await self.connection.execute("""
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status_code = 200 THEN 1 ELSE 0 END) as success,
                AVG(response_time) as avg_response_time
            FROM spider_results
        """)
        stats_row = await cursor.fetchone()

        # 状态码分布
        cursor = await self.connection.execute("""
            SELECT status_code, COUNT(*) as count
            FROM spider_results
            GROUP BY status_code
            ORDER BY count DESC
        """)
        status_distribution = dict(await cursor.fetchall())

        return {
            'total_results': total_count,
            'successful_results': stats_row[1] or 0,
            'success_rate': (stats_row[1] or 0) / max(stats_row[0], 1),
            'avg_response_time': stats_row[2] or 0,
            'status_distribution': status_distribution
        }

# 异步文件存储
# 文件：async_spider/storage/async_file.py
import aiofiles
import json
import csv
from typing import List, Dict, Any
from pathlib import Path

class AsyncFileStorage:
    """异步文件存储"""

    def __init__(self, base_dir: str = "spider_data"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)

    async def save_json(self, data: Any, filename: str) -> str:
        """保存JSON文件"""
        file_path = self.base_dir / filename

        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(data, ensure_ascii=False, indent=2))

        return str(file_path)

    async def save_csv(self, data: List[Dict[str, Any]], filename: str) -> str:
        """保存CSV文件"""
        if not data:
            return ""

        file_path = self.base_dir / filename

        # 获取所有字段名
        fieldnames = set()
        for item in data:
            fieldnames.update(item.keys())
        fieldnames = sorted(fieldnames)

        async with aiofiles.open(file_path, 'w', encoding='utf-8', newline='') as f:
            # 写入CSV头部
            header_line = ','.join(fieldnames) + '\n'
            await f.write(header_line)

            # 写入数据行
            for item in data:
                row_values = []
                for field in fieldnames:
                    value = item.get(field, '')
                    # 处理特殊字符
                    if isinstance(value, str):
                        value = value.replace('"', '""')
                        if ',' in value or '"' in value or '\n' in value:
                            value = f'"{value}"'
                    row_values.append(str(value))

                row_line = ','.join(row_values) + '\n'
                await f.write(row_line)

        return str(file_path)

    async def save_text(self, content: str, filename: str) -> str:
        """保存文本文件"""
        file_path = self.base_dir / filename

        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(content)

        return str(file_path)

    async def load_json(self, filename: str) -> Any:
        """加载JSON文件"""
        file_path = self.base_dir / filename

        if not file_path.exists():
            return None

        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)

# 性能监控
# 文件：async_spider/utils/monitor.py
import asyncio
import time
import psutil
import logging
from typing import Dict, Any, List
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    active_tasks: int
    completed_tasks: int
    requests_per_second: float
    avg_response_time: float

class AsyncPerformanceMonitor:
    """异步性能监控器"""

    def __init__(self, sample_interval: float = 1.0):
        self.sample_interval = sample_interval
        self.metrics_history: List[PerformanceMetrics] = []
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None

        # 统计计数器
        self.total_requests = 0
        self.completed_requests = 0
        self.total_response_time = 0.0
        self.start_time = time.time()

    async def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("性能监控已启动")

    async def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return

        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass

        logger.info("性能监控已停止")

    async def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                cpu_percent = psutil.cpu_percent()
                memory_info = psutil.virtual_memory()
                memory_percent = memory_info.percent
                memory_mb = memory_info.used / 1024 / 1024

                # 收集任务指标
                active_tasks = len([task for task in asyncio.all_tasks() if not task.done()])

                # 计算请求速率
                elapsed_time = time.time() - self.start_time
                requests_per_second = self.completed_requests / max(elapsed_time, 1)

                # 计算平均响应时间
                avg_response_time = (
                    self.total_response_time / max(self.completed_requests, 1)
                    if self.completed_requests > 0 else 0
                )

                # 创建性能指标
                metrics = PerformanceMetrics(
                    timestamp=time.time(),
                    cpu_percent=cpu_percent,
                    memory_percent=memory_percent,
                    memory_mb=memory_mb,
                    active_tasks=active_tasks,
                    completed_tasks=self.completed_requests,
                    requests_per_second=requests_per_second,
                    avg_response_time=avg_response_time
                )

                self.metrics_history.append(metrics)

                # 保持历史记录在合理范围内
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-500:]

                await asyncio.sleep(self.sample_interval)

            except Exception as e:
                logger.error(f"性能监控错误: {e}")
                await asyncio.sleep(self.sample_interval)

    def record_request(self, response_time: float):
        """记录请求"""
        self.total_requests += 1
        self.completed_requests += 1
        self.total_response_time += response_time

    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前指标"""
        return self.metrics_history[-1] if self.metrics_history else None

    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计"""
        if not self.metrics_history:
            return {}

        cpu_values = [m.cpu_percent for m in self.metrics_history]
        memory_values = [m.memory_percent for m in self.metrics_history]
        rps_values = [m.requests_per_second for m in self.metrics_history]

        return {
            'monitoring_duration': time.time() - self.start_time,
            'total_samples': len(self.metrics_history),
            'cpu_stats': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory_stats': {
                'avg': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'min': min(memory_values)
            },
            'requests_stats': {
                'total_requests': self.total_requests,
                'completed_requests': self.completed_requests,
                'avg_rps': sum(rps_values) / len(rps_values) if rps_values else 0,
                'max_rps': max(rps_values) if rps_values else 0
            }
        }

# 测试异步存储和监控
async def test_async_storage_and_monitoring():
    """测试异步存储和监控"""
    print("=== 异步存储和监控测试 ===")

    # 创建测试数据
    from async_spider.core.spider import SpiderResult

    test_results = [
        SpiderResult(
            url=f"http://example.com/{i}",
            status_code=200,
            content=f"<html><title>页面 {i}</title></html>",
            extracted_data={"title": f"页面 {i}", "links": []},
            response_time=0.5 + i * 0.1
        )
        for i in range(10)
    ]

    # 测试数据库存储
    print("\n1. 测试数据库存储")
    async with AsyncDatabaseStorage("test_spider.db") as db_storage:
        # 批量保存
        await db_storage.save_results_batch(test_results)

        # 获取统计信息
        stats = await db_storage.get_statistics()
        print(f"数据库统计: {stats}")

        # 获取结果
        results = await db_storage.get_results(limit=5)
        print(f"获取到 {len(results)} 条结果")

    # 测试文件存储
    print("\n2. 测试文件存储")
    file_storage = AsyncFileStorage("test_data")

    # 保存JSON
    json_data = [{"url": r.url, "status": r.status_code} for r in test_results]
    json_file = await file_storage.save_json(json_data, "results.json")
    print(f"JSON文件已保存: {json_file}")

    # 保存CSV
    csv_data = [
        {
            "url": r.url,
            "status_code": r.status_code,
            "response_time": r.response_time,
            "title": r.extracted_data.get("title", "") if r.extracted_data else ""
        }
        for r in test_results
    ]
    csv_file = await file_storage.save_csv(csv_data, "results.csv")
    print(f"CSV文件已保存: {csv_file}")

    # 测试性能监控
    print("\n3. 测试性能监控")
    monitor = AsyncPerformanceMonitor(sample_interval=0.5)

    await monitor.start_monitoring()

    # 模拟一些工作负载
    for i in range(5):
        await asyncio.sleep(0.3)
        monitor.record_request(0.2 + i * 0.1)

    await monitor.stop_monitoring()

    # 获取监控统计
    summary = monitor.get_summary_stats()
    print(f"性能监控统计: {summary}")

if __name__ == "__main__":
    asyncio.run(test_async_storage_and_monitoring())
```

---

## ✅ 下午学习检查清单

### 异步编程理论 (必须理解)
- [ ] 理解异步编程的核心概念和优势
- [ ] 掌握协程、事件循环、任务的关系
- [ ] 理解async/await语法和使用场景
- [ ] 知道异步编程与多线程的区别
- [ ] 理解异步上下文管理器的使用

### 异步HTTP技能 (必须会做)
- [ ] 能使用aiohttp进行异步HTTP请求
- [ ] 能配置异步HTTP客户端和连接池
- [ ] 能实现异步请求的重试和错误处理
- [ ] 能进行并发控制和限流
- [ ] 能监控异步请求的性能指标

### 异步爬虫技术 (核心能力)
- [ ] 能设计和实现异步爬虫架构
- [ ] 能实现令牌桶限流算法
- [ ] 能处理大规模并发爬取任务
- [ ] 能实现智能的数据提取器
- [ ] 能优化爬虫的内存和CPU使用

### 异步数据处理 (高级技能)
- [ ] 能实现异步数据库操作
- [ ] 能进行异步文件读写
- [ ] 能设计高效的数据存储策略
- [ ] 能实现实时性能监控
- [ ] 能处理大量数据的批量操作

### 性能优化能力 (专家级)
- [ ] 能分析和优化异步程序性能
- [ ] 能设计合理的并发控制策略
- [ ] 能监控系统资源使用情况
- [ ] 能识别和解决性能瓶颈
- [ ] 能进行异步程序的压力测试

---

## 🚀 下午总结与晚上预告

### 🎯 下午成果
完成了高性能异步爬虫技术的全面掌握：
1. ✅ 深入理解了异步编程的核心原理
2. ✅ 掌握了aiohttp异步HTTP客户端技术
3. ✅ 实现了完整的异步爬虫架构
4. ✅ 开发了令牌桶限流和并发控制
5. ✅ 实现了异步数据存储和文件操作
6. ✅ 构建了实时性能监控系统
7. ✅ 编写了全面的测试和验证代码

### 🌙 晚上预告
晚上将进行系统整合和性能优化：
- 整合JWT认证、爬虫和数据库系统
- 实现完整的异步API接口
- 进行系统性能测试和优化
- 部署和监控完整系统

### 💡 实践建议
1. **立即实践** (30分钟)：
   - 运行异步HTTP客户端测试
   - 执行异步爬虫示例
   - 测试异步数据存储功能

2. **深入理解** (20分钟)：
   - 观看推荐的异步编程视频
   - 分析异步和同步性能差异
   - 理解事件循环的工作机制

3. **扩展练习** (20分钟)：
   - 实现更复杂的数据提取器
   - 优化并发控制策略
   - 添加更多性能监控指标

### 📊 性能提升效果
通过异步技术，你应该能够实现：
- **并发性能**: 10-100倍提升
- **资源利用率**: 显著提高
- **响应时间**: 大幅降低
- **吞吐量**: 成倍增长

### 🎓 技能等级评估
完成下午学习后，你将达到：
- 🔰 **初级**: 理解异步编程基础
- 🥉 **中级**: 能开发异步应用
- 🥈 **高级**: 能优化异步性能
- 🥇 **专家**: 能设计异步架构

### 🔥 异步编程最佳实践
1. **合理使用异步**：
   - I/O密集型任务使用异步
   - CPU密集型任务考虑多进程
   - 避免在异步函数中使用阻塞操作

2. **并发控制**：
   - 使用信号量控制并发数
   - 实现合理的限流策略
   - 监控资源使用情况

3. **错误处理**：
   - 使用try-except处理异常
   - 实现重试机制
   - 记录详细的错误日志

**记住**：异步编程是现代高性能应用的核心技术，掌握它将让你的程序性能飞跃！

---

## 📚 进阶学习资源

### 📖 深入阅读
- 《Python异步编程》- 异步编程权威指南
- 《高性能Python》- 性能优化实战
- 《aiohttp官方文档》- 异步HTTP库详解

### 🎥 视频教程推荐
- "Python异步编程实战课程" - 极客时间
- "高并发系统设计" - 拉勾教育
- "异步爬虫项目实战" - B站技术UP主

### 🛠️ 实战项目
- 开发分布式异步爬虫系统
- 实现高并发API服务
- 构建实时数据处理管道

**准备好进行系统整合和最终优化了吗？** 🚀✨
