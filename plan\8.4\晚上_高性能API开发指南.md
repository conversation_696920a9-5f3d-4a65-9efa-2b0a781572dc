# 🚀 高性能API开发指南 (晚上专用)

## 🎯 学习目标：整合异步技术构建高性能API系统

### 📚 第一步：理解异步API的优势 (15分钟)

**为什么需要异步API？**
- 异步API = 非阻塞式API，能同时处理大量并发请求
- 核心优势：高并发、低延迟、资源利用率高
- 适用场景：I/O密集型操作、数据库查询、外部API调用

**同步 vs 异步API性能对比**：
```python
# 同步API（阻塞式）
@app.get("/sync/videos")
def get_videos_sync():
    # 数据库查询阻塞
    videos = db.query(Video).all()  # 阻塞100ms
    # 外部API调用阻塞
    for video in videos:
        metadata = requests.get(f"api.com/video/{video.id}")  # 阻塞200ms
    return videos
# 总耗时：100ms + (200ms × 视频数量)

# 异步API（非阻塞式）
@app.get("/async/videos")
async def get_videos_async():
    # 异步数据库查询
    videos = await db.query(Video).all()  # 非阻塞100ms
    # 并发外部API调用
    tasks = [fetch_metadata(video.id) for video in videos]
    metadata_list = await asyncio.gather(*tasks)  # 并发执行200ms
    return videos
# 总耗时：100ms + 200ms（无论多少视频）
```

**异步API架构设计原则**：
- **无阻塞原则**：所有I/O操作都使用异步
- **并发控制**：合理限制并发数量
- **错误隔离**：单个请求失败不影响其他请求
- **资源管理**：连接池、内存管理、任务清理

---

## 🔧 第二步：异步API重构 (45分钟)

### 异步视频管理API
```python
# 文件：app/api/async_videos.py
from fastapi import FastAPI, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from typing import List, Dict, Any, Optional
import asyncio
import aiohttp
import time
import logging

from app.database.async_connection import get_async_db
from app.models.async_models import AsyncVideo, AsyncUser
from app.middleware.auth import get_current_active_user
from app.models.auth import TokenData

logger = logging.getLogger(__name__)

class AsyncVideoService:
    """异步视频服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.http_session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=10)
        self.http_session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.http_session:
            await self.http_session.close()
    
    async def get_videos_with_metadata(
        self, 
        user_id: int, 
        page: int = 1, 
        size: int = 20
    ) -> Dict[str, Any]:
        """获取带元数据的视频列表"""
        start_time = time.time()
        
        try:
            # 异步查询视频
            offset = (page - 1) * size
            query = select(AsyncVideo).where(
                and_(
                    AsyncVideo.user_id == user_id,
                    AsyncVideo.status == 'active'
                )
            ).offset(offset).limit(size)
            
            result = await self.db.execute(query)
            videos = result.scalars().all()
            
            # 并发获取视频元数据
            if videos:
                metadata_tasks = [
                    self._fetch_video_metadata(video.url) 
                    for video in videos
                ]
                metadata_list = await asyncio.gather(
                    *metadata_tasks, 
                    return_exceptions=True
                )
                
                # 合并数据
                enriched_videos = []
                for video, metadata in zip(videos, metadata_list):
                    video_dict = {
                        'id': video.id,
                        'title': video.title,
                        'url': video.url,
                        'duration': video.duration,
                        'view_count': video.view_count,
                        'created_at': video.created_at.isoformat()
                    }
                    
                    if isinstance(metadata, dict):
                        video_dict['metadata'] = metadata
                    else:
                        video_dict['metadata'] = {'error': str(metadata)}
                    
                    enriched_videos.append(video_dict)
            else:
                enriched_videos = []
            
            # 获取总数（异步）
            count_query = select(func.count(AsyncVideo.id)).where(
                and_(
                    AsyncVideo.user_id == user_id,
                    AsyncVideo.status == 'active'
                )
            )
            count_result = await self.db.execute(count_query)
            total_count = count_result.scalar()
            
            processing_time = time.time() - start_time
            
            return {
                'videos': enriched_videos,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total_count,
                    'pages': (total_count + size - 1) // size
                },
                'performance': {
                    'processing_time': round(processing_time, 3),
                    'video_count': len(videos)
                }
            }
            
        except Exception as e:
            logger.error(f"获取视频列表失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取视频列表失败: {str(e)}"
            )
    
    async def _fetch_video_metadata(self, video_url: str) -> Dict[str, Any]:
        """获取视频元数据"""
        try:
            # 模拟外部API调用
            metadata_api_url = f"https://api.example.com/video/metadata"
            
            async with self.http_session.get(
                metadata_api_url,
                params={'url': video_url}
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {'error': f'API返回状态码: {response.status}'}
                    
        except asyncio.TimeoutError:
            return {'error': '获取元数据超时'}
        except Exception as e:
            return {'error': f'获取元数据失败: {str(e)}'}
    
    async def create_video_async(
        self, 
        video_data: Dict[str, Any], 
        user_id: int
    ) -> Dict[str, Any]:
        """异步创建视频"""
        try:
            # 验证视频URL（异步）
            is_valid = await self._validate_video_url(video_data['url'])
            if not is_valid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的视频URL"
                )
            
            # 创建视频记录
            new_video = AsyncVideo(
                title=video_data['title'],
                url=video_data['url'],
                description=video_data.get('description'),
                user_id=user_id
            )
            
            self.db.add(new_video)
            await self.db.commit()
            await self.db.refresh(new_video)
            
            return {
                'id': new_video.id,
                'title': new_video.title,
                'url': new_video.url,
                'created_at': new_video.created_at.isoformat()
            }
            
        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建视频失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建视频失败: {str(e)}"
            )
    
    async def _validate_video_url(self, url: str) -> bool:
        """验证视频URL"""
        try:
            async with self.http_session.head(url) as response:
                return response.status == 200
        except:
            return False
    
    async def batch_update_view_counts(
        self, 
        video_updates: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """批量更新观看次数"""
        try:
            # 构建批量更新语句
            update_tasks = []
            for update in video_updates:
                video_id = update['video_id']
                increment = update['increment']
                
                # 异步更新单个视频
                task = self._update_single_view_count(video_id, increment)
                update_tasks.append(task)
            
            # 并发执行更新
            results = await asyncio.gather(*update_tasks, return_exceptions=True)
            
            # 统计结果
            successful_updates = sum(1 for r in results if r is True)
            failed_updates = len(results) - successful_updates
            
            await self.db.commit()
            
            return {
                'total_updates': len(video_updates),
                'successful_updates': successful_updates,
                'failed_updates': failed_updates
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"批量更新失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"批量更新失败: {str(e)}"
            )
    
    async def _update_single_view_count(self, video_id: int, increment: int) -> bool:
        """更新单个视频观看次数"""
        try:
            video = await self.db.get(AsyncVideo, video_id)
            if video:
                video.view_count += increment
                return True
            return False
        except Exception as e:
            logger.error(f"更新视频 {video_id} 观看次数失败: {e}")
            return False

# FastAPI路由定义
from fastapi import APIRouter

router = APIRouter(prefix="/async/videos", tags=["异步视频管理"])

@router.get("/", response_model=Dict[str, Any])
async def get_user_videos_async(
    page: int = 1,
    size: int = 20,
    db: AsyncSession = Depends(get_async_db),
    current_user: TokenData = Depends(get_current_active_user)
):
    """获取用户视频列表（异步版本）"""
    async with AsyncVideoService(db) as service:
        return await service.get_videos_with_metadata(
            user_id=current_user.user_id,
            page=page,
            size=size
        )

@router.post("/", response_model=Dict[str, Any])
async def create_video_async(
    video_data: Dict[str, Any],
    db: AsyncSession = Depends(get_async_db),
    current_user: TokenData = Depends(get_current_active_user)
):
    """创建视频（异步版本）"""
    async with AsyncVideoService(db) as service:
        return await service.create_video_async(
            video_data=video_data,
            user_id=current_user.user_id
        )

@router.patch("/batch-update-views", response_model=Dict[str, Any])
async def batch_update_views_async(
    updates: List[Dict[str, Any]],
    db: AsyncSession = Depends(get_async_db),
    current_user: TokenData = Depends(get_current_active_user)
):
    """批量更新观看次数（异步版本）"""
    async with AsyncVideoService(db) as service:
        return await service.batch_update_view_counts(updates)

# 性能监控中间件
from fastapi import Request, Response
import time

@router.middleware("http")
async def performance_middleware(request: Request, call_next):
    """性能监控中间件"""
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(round(process_time, 3))
    
    # 记录慢请求
    if process_time > 1.0:
        logger.warning(
            f"慢请求检测: {request.method} {request.url} "
            f"耗时 {process_time:.3f}s"
        )
    
    return response
```

---

## 🕷️ 第三步：异步爬虫任务队列 (45分钟)

### 异步任务队列服务
```python
# 文件：app/services/async_spider_service.py
import asyncio
import uuid
import json
import time
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
import logging

from app.database.async_connection import get_async_db
from app.models.async_models import AsyncSpiderTask, AsyncSpiderResult
from async_spider.core.spider import AsyncSpider, SpiderConfig, SpiderResult

logger = logging.getLogger(__name__)

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskProgress:
    """任务进度"""
    task_id: str
    status: TaskStatus
    total_urls: int = 0
    completed_urls: int = 0
    successful_urls: int = 0
    failed_urls: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None

class AsyncSpiderTaskQueue:
    """异步爬虫任务队列"""

    def __init__(self):
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_progress: Dict[str, TaskProgress] = {}
        self.max_concurrent_tasks = 5
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        self.result_callbacks: Dict[str, List[Callable]] = {}

    async def submit_task(
        self,
        task_id: str,
        urls: List[str],
        config: Dict[str, Any],
        user_id: int,
        db: AsyncSession
    ) -> str:
        """提交爬虫任务"""
        try:
            # 创建任务进度跟踪
            progress = TaskProgress(
                task_id=task_id,
                status=TaskStatus.PENDING,
                total_urls=len(urls)
            )
            self.task_progress[task_id] = progress

            # 保存任务到数据库
            db_task = AsyncSpiderTask(
                id=task_id,
                user_id=user_id,
                urls=json.dumps(urls),
                config=json.dumps(config),
                status=TaskStatus.PENDING.value,
                total_urls=len(urls)
            )
            db.add(db_task)
            await db.commit()

            # 创建异步任务
            task = asyncio.create_task(
                self._execute_spider_task(task_id, urls, config, user_id, db)
            )
            self.running_tasks[task_id] = task

            logger.info(f"任务 {task_id} 已提交到队列")
            return task_id

        except Exception as e:
            logger.error(f"提交任务失败: {e}")
            raise

    async def _execute_spider_task(
        self,
        task_id: str,
        urls: List[str],
        config: Dict[str, Any],
        user_id: int,
        db: AsyncSession
    ):
        """执行爬虫任务"""
        async with self.semaphore:  # 控制并发任务数
            progress = self.task_progress[task_id]

            try:
                # 更新任务状态为运行中
                progress.status = TaskStatus.RUNNING
                progress.start_time = datetime.now()

                await self._update_task_in_db(task_id, {
                    'status': TaskStatus.RUNNING.value,
                    'started_at': progress.start_time
                }, db)

                # 配置爬虫
                spider_config = SpiderConfig(
                    max_concurrent=config.get('max_concurrent', 10),
                    request_delay=config.get('request_delay', 0.5),
                    max_retries=config.get('max_retries', 3)
                )

                # 执行爬虫任务
                async with AsyncSpider(spider_config) as spider:
                    results = await spider.crawl_urls(
                        urls,
                        extractor=self._create_progress_callback(task_id)
                    )

                # 处理结果
                await self._process_results(task_id, results, user_id, db)

                # 更新任务状态为完成
                progress.status = TaskStatus.COMPLETED
                progress.end_time = datetime.now()
                progress.completed_urls = len(results)
                progress.successful_urls = sum(1 for r in results if not r.error)
                progress.failed_urls = sum(1 for r in results if r.error)

                await self._update_task_in_db(task_id, {
                    'status': TaskStatus.COMPLETED.value,
                    'completed_at': progress.end_time,
                    'success_count': progress.successful_urls,
                    'failed_count': progress.failed_urls
                }, db)

                # 执行回调
                await self._execute_callbacks(task_id, results)

                logger.info(f"任务 {task_id} 执行完成")

            except asyncio.CancelledError:
                progress.status = TaskStatus.CANCELLED
                await self._update_task_in_db(task_id, {
                    'status': TaskStatus.CANCELLED.value
                }, db)
                logger.info(f"任务 {task_id} 被取消")

            except Exception as e:
                progress.status = TaskStatus.FAILED
                progress.error_message = str(e)
                progress.end_time = datetime.now()

                await self._update_task_in_db(task_id, {
                    'status': TaskStatus.FAILED.value,
                    'completed_at': progress.end_time,
                    'error_message': str(e)
                }, db)

                logger.error(f"任务 {task_id} 执行失败: {e}")

            finally:
                # 清理任务
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]

    def _create_progress_callback(self, task_id: str) -> Callable:
        """创建进度回调函数"""
        async def progress_callback(content: str, url: str) -> Dict[str, Any]:
            # 更新进度
            if task_id in self.task_progress:
                self.task_progress[task_id].completed_urls += 1

            # 简单的数据提取
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')
            title = soup.find('title')

            return {
                'title': title.text.strip() if title else '',
                'url': url,
                'content_length': len(content),
                'extracted_at': datetime.now().isoformat()
            }

        return progress_callback

    async def _process_results(
        self,
        task_id: str,
        results: List[SpiderResult],
        user_id: int,
        db: AsyncSession
    ):
        """处理爬虫结果"""
        try:
            # 批量保存结果到数据库
            db_results = []
            for result in results:
                db_result = AsyncSpiderResult(
                    task_id=task_id,
                    url=result.url,
                    status_code=result.status_code,
                    success=not bool(result.error),
                    response_time=result.response_time,
                    extracted_data=json.dumps(result.extracted_data) if result.extracted_data else None,
                    error_message=result.error
                )
                db_results.append(db_result)

            db.add_all(db_results)
            await db.commit()

            logger.info(f"任务 {task_id} 的 {len(results)} 个结果已保存")

        except Exception as e:
            logger.error(f"保存任务结果失败: {e}")
            await db.rollback()
            raise

    async def _update_task_in_db(
        self,
        task_id: str,
        updates: Dict[str, Any],
        db: AsyncSession
    ):
        """更新数据库中的任务状态"""
        try:
            stmt = update(AsyncSpiderTask).where(
                AsyncSpiderTask.id == task_id
            ).values(**updates)

            await db.execute(stmt)
            await db.commit()

        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            await db.rollback()

    async def _execute_callbacks(self, task_id: str, results: List[SpiderResult]):
        """执行任务完成回调"""
        if task_id in self.result_callbacks:
            for callback in self.result_callbacks[task_id]:
                try:
                    await callback(task_id, results)
                except Exception as e:
                    logger.error(f"执行回调失败: {e}")

    def add_result_callback(self, task_id: str, callback: Callable):
        """添加结果回调"""
        if task_id not in self.result_callbacks:
            self.result_callbacks[task_id] = []
        self.result_callbacks[task_id].append(callback)

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.cancel()
            return True
        return False

    def get_task_progress(self, task_id: str) -> Optional[TaskProgress]:
        """获取任务进度"""
        return self.task_progress.get(task_id)

    def get_all_tasks_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        status = {}
        for task_id, progress in self.task_progress.items():
            status[task_id] = {
                'status': progress.status.value,
                'total_urls': progress.total_urls,
                'completed_urls': progress.completed_urls,
                'successful_urls': progress.successful_urls,
                'failed_urls': progress.failed_urls,
                'start_time': progress.start_time.isoformat() if progress.start_time else None,
                'end_time': progress.end_time.isoformat() if progress.end_time else None,
                'error_message': progress.error_message
            }
        return status

# 全局任务队列实例
task_queue = AsyncSpiderTaskQueue()

# FastAPI集成
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from app.middleware.auth import get_current_active_user
from app.models.auth import TokenData

spider_router = APIRouter(prefix="/async/spider", tags=["异步爬虫任务"])

@spider_router.post("/tasks", response_model=Dict[str, Any])
async def create_spider_task_async(
    task_data: Dict[str, Any],
    db: AsyncSession = Depends(get_async_db),
    current_user: TokenData = Depends(get_current_active_user)
):
    """创建异步爬虫任务"""
    try:
        task_id = str(uuid.uuid4())
        urls = task_data['urls']
        config = task_data.get('config', {})

        await task_queue.submit_task(
            task_id=task_id,
            urls=urls,
            config=config,
            user_id=current_user.user_id,
            db=db
        )

        return {
            'success': True,
            'message': '异步爬虫任务创建成功',
            'data': {
                'task_id': task_id,
                'status': 'pending',
                'total_urls': len(urls)
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"创建任务失败: {str(e)}"
        )

@spider_router.get("/tasks/{task_id}/progress", response_model=Dict[str, Any])
async def get_task_progress_async(
    task_id: str,
    current_user: TokenData = Depends(get_current_active_user)
):
    """获取任务进度"""
    progress = task_queue.get_task_progress(task_id)

    if not progress:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )

    return {
        'success': True,
        'data': {
            'task_id': task_id,
            'status': progress.status.value,
            'total_urls': progress.total_urls,
            'completed_urls': progress.completed_urls,
            'successful_urls': progress.successful_urls,
            'failed_urls': progress.failed_urls,
            'progress_percentage': (progress.completed_urls / max(progress.total_urls, 1)) * 100,
            'start_time': progress.start_time.isoformat() if progress.start_time else None,
            'end_time': progress.end_time.isoformat() if progress.end_time else None,
            'error_message': progress.error_message
        }
    }

@spider_router.delete("/tasks/{task_id}", response_model=Dict[str, Any])
async def cancel_task_async(
    task_id: str,
    current_user: TokenData = Depends(get_current_active_user)
):
    """取消任务"""
    success = await task_queue.cancel_task(task_id)

    if success:
        return {
            'success': True,
            'message': '任务取消成功'
        }
    else:
        return {
            'success': False,
            'message': '任务不存在或已完成'
        }

@spider_router.get("/tasks/status", response_model=Dict[str, Any])
async def get_all_tasks_status_async(
    current_user: TokenData = Depends(get_current_active_user)
):
    """获取所有任务状态"""
    status = task_queue.get_all_tasks_status()

    return {
        'success': True,
        'data': {
            'tasks': status,
            'total_tasks': len(status),
            'running_tasks': len([t for t in status.values() if t['status'] == 'running'])
        }
    }
```
