# 🚀 高性能API开发指南 (晚上专用)

## 🎯 学习目标：整合异步技术构建高性能API系统

### 📚 第一步：理解异步API的优势 (15分钟)

**为什么需要异步API？**
- 异步API = 非阻塞式API，能同时处理大量并发请求
- 核心优势：高并发、低延迟、资源利用率高
- 适用场景：I/O密集型操作、数据库查询、外部API调用

**同步 vs 异步API性能对比**：
```python
# 同步API（阻塞式）
@app.get("/sync/videos")
def get_videos_sync():
    # 数据库查询阻塞
    videos = db.query(Video).all()  # 阻塞100ms
    # 外部API调用阻塞
    for video in videos:
        metadata = requests.get(f"api.com/video/{video.id}")  # 阻塞200ms
    return videos
# 总耗时：100ms + (200ms × 视频数量)

# 异步API（非阻塞式）
@app.get("/async/videos")
async def get_videos_async():
    # 异步数据库查询
    videos = await db.query(Video).all()  # 非阻塞100ms
    # 并发外部API调用
    tasks = [fetch_metadata(video.id) for video in videos]
    metadata_list = await asyncio.gather(*tasks)  # 并发执行200ms
    return videos
# 总耗时：100ms + 200ms（无论多少视频）
```

**异步API架构设计原则**：
- **无阻塞原则**：所有I/O操作都使用异步
- **并发控制**：合理限制并发数量
- **错误隔离**：单个请求失败不影响其他请求
- **资源管理**：连接池、内存管理、任务清理

---

## 🔧 第二步：异步API重构 (45分钟)

### 异步视频管理API
```python
# 文件：app/api/async_videos.py
from fastapi import FastAPI, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from typing import List, Dict, Any, Optional
import asyncio
import aiohttp
import time
import logging

from app.database.async_connection import get_async_db
from app.models.async_models import AsyncVideo, AsyncUser
from app.middleware.auth import get_current_active_user
from app.models.auth import TokenData

logger = logging.getLogger(__name__)

class AsyncVideoService:
    """异步视频服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.http_session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=10)
        self.http_session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.http_session:
            await self.http_session.close()
    
    async def get_videos_with_metadata(
        self, 
        user_id: int, 
        page: int = 1, 
        size: int = 20
    ) -> Dict[str, Any]:
        """获取带元数据的视频列表"""
        start_time = time.time()
        
        try:
            # 异步查询视频
            offset = (page - 1) * size
            query = select(AsyncVideo).where(
                and_(
                    AsyncVideo.user_id == user_id,
                    AsyncVideo.status == 'active'
                )
            ).offset(offset).limit(size)
            
            result = await self.db.execute(query)
            videos = result.scalars().all()
            
            # 并发获取视频元数据
            if videos:
                metadata_tasks = [
                    self._fetch_video_metadata(video.url) 
                    for video in videos
                ]
                metadata_list = await asyncio.gather(
                    *metadata_tasks, 
                    return_exceptions=True
                )
                
                # 合并数据
                enriched_videos = []
                for video, metadata in zip(videos, metadata_list):
                    video_dict = {
                        'id': video.id,
                        'title': video.title,
                        'url': video.url,
                        'duration': video.duration,
                        'view_count': video.view_count,
                        'created_at': video.created_at.isoformat()
                    }
                    
                    if isinstance(metadata, dict):
                        video_dict['metadata'] = metadata
                    else:
                        video_dict['metadata'] = {'error': str(metadata)}
                    
                    enriched_videos.append(video_dict)
            else:
                enriched_videos = []
            
            # 获取总数（异步）
            count_query = select(func.count(AsyncVideo.id)).where(
                and_(
                    AsyncVideo.user_id == user_id,
                    AsyncVideo.status == 'active'
                )
            )
            count_result = await self.db.execute(count_query)
            total_count = count_result.scalar()
            
            processing_time = time.time() - start_time
            
            return {
                'videos': enriched_videos,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total_count,
                    'pages': (total_count + size - 1) // size
                },
                'performance': {
                    'processing_time': round(processing_time, 3),
                    'video_count': len(videos)
                }
            }
            
        except Exception as e:
            logger.error(f"获取视频列表失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取视频列表失败: {str(e)}"
            )
    
    async def _fetch_video_metadata(self, video_url: str) -> Dict[str, Any]:
        """获取视频元数据"""
        try:
            # 模拟外部API调用
            metadata_api_url = f"https://api.example.com/video/metadata"
            
            async with self.http_session.get(
                metadata_api_url,
                params={'url': video_url}
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {'error': f'API返回状态码: {response.status}'}
                    
        except asyncio.TimeoutError:
            return {'error': '获取元数据超时'}
        except Exception as e:
            return {'error': f'获取元数据失败: {str(e)}'}
    
    async def create_video_async(
        self, 
        video_data: Dict[str, Any], 
        user_id: int
    ) -> Dict[str, Any]:
        """异步创建视频"""
        try:
            # 验证视频URL（异步）
            is_valid = await self._validate_video_url(video_data['url'])
            if not is_valid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的视频URL"
                )
            
            # 创建视频记录
            new_video = AsyncVideo(
                title=video_data['title'],
                url=video_data['url'],
                description=video_data.get('description'),
                user_id=user_id
            )
            
            self.db.add(new_video)
            await self.db.commit()
            await self.db.refresh(new_video)
            
            return {
                'id': new_video.id,
                'title': new_video.title,
                'url': new_video.url,
                'created_at': new_video.created_at.isoformat()
            }
            
        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建视频失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建视频失败: {str(e)}"
            )
    
    async def _validate_video_url(self, url: str) -> bool:
        """验证视频URL"""
        try:
            async with self.http_session.head(url) as response:
                return response.status == 200
        except:
            return False
    
    async def batch_update_view_counts(
        self, 
        video_updates: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """批量更新观看次数"""
        try:
            # 构建批量更新语句
            update_tasks = []
            for update in video_updates:
                video_id = update['video_id']
                increment = update['increment']
                
                # 异步更新单个视频
                task = self._update_single_view_count(video_id, increment)
                update_tasks.append(task)
            
            # 并发执行更新
            results = await asyncio.gather(*update_tasks, return_exceptions=True)
            
            # 统计结果
            successful_updates = sum(1 for r in results if r is True)
            failed_updates = len(results) - successful_updates
            
            await self.db.commit()
            
            return {
                'total_updates': len(video_updates),
                'successful_updates': successful_updates,
                'failed_updates': failed_updates
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"批量更新失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"批量更新失败: {str(e)}"
            )
    
    async def _update_single_view_count(self, video_id: int, increment: int) -> bool:
        """更新单个视频观看次数"""
        try:
            video = await self.db.get(AsyncVideo, video_id)
            if video:
                video.view_count += increment
                return True
            return False
        except Exception as e:
            logger.error(f"更新视频 {video_id} 观看次数失败: {e}")
            return False

# FastAPI路由定义
from fastapi import APIRouter

router = APIRouter(prefix="/async/videos", tags=["异步视频管理"])

@router.get("/", response_model=Dict[str, Any])
async def get_user_videos_async(
    page: int = 1,
    size: int = 20,
    db: AsyncSession = Depends(get_async_db),
    current_user: TokenData = Depends(get_current_active_user)
):
    """获取用户视频列表（异步版本）"""
    async with AsyncVideoService(db) as service:
        return await service.get_videos_with_metadata(
            user_id=current_user.user_id,
            page=page,
            size=size
        )

@router.post("/", response_model=Dict[str, Any])
async def create_video_async(
    video_data: Dict[str, Any],
    db: AsyncSession = Depends(get_async_db),
    current_user: TokenData = Depends(get_current_active_user)
):
    """创建视频（异步版本）"""
    async with AsyncVideoService(db) as service:
        return await service.create_video_async(
            video_data=video_data,
            user_id=current_user.user_id
        )

@router.patch("/batch-update-views", response_model=Dict[str, Any])
async def batch_update_views_async(
    updates: List[Dict[str, Any]],
    db: AsyncSession = Depends(get_async_db),
    current_user: TokenData = Depends(get_current_active_user)
):
    """批量更新观看次数（异步版本）"""
    async with AsyncVideoService(db) as service:
        return await service.batch_update_view_counts(updates)

# 性能监控中间件
from fastapi import Request, Response
import time

@router.middleware("http")
async def performance_middleware(request: Request, call_next):
    """性能监控中间件"""
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(round(process_time, 3))
    
    # 记录慢请求
    if process_time > 1.0:
        logger.warning(
            f"慢请求检测: {request.method} {request.url} "
            f"耗时 {process_time:.3f}s"
        )
    
    return response
```

---

## 🕷️ 第三步：异步爬虫任务队列 (45分钟)

### 异步任务队列服务
```python
# 文件：app/services/async_spider_service.py
import asyncio
import uuid
import json
import time
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from async_spider.core.spider import AsyncSpider, SpiderConfig

logger = logging.getLogger(__name__)

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskProgress:
    """任务进度"""
    task_id: str
    status: TaskStatus
    total_urls: int = 0
    completed_urls: int = 0
    successful_urls: int = 0
    failed_urls: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

class AsyncSpiderTaskQueue:
    """异步爬虫任务队列"""

    def __init__(self):
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_progress: Dict[str, TaskProgress] = {}
        self.max_concurrent_tasks = 5
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)

    async def submit_task(
        self,
        urls: List[str],
        config: Dict[str, Any],
        user_id: int
    ) -> str:
        """提交爬虫任务"""
        task_id = str(uuid.uuid4())

        # 创建任务进度跟踪
        progress = TaskProgress(
            task_id=task_id,
            status=TaskStatus.PENDING,
            total_urls=len(urls)
        )
        self.task_progress[task_id] = progress

        # 创建异步任务
        task = asyncio.create_task(
            self._execute_spider_task(task_id, urls, config, user_id)
        )
        self.running_tasks[task_id] = task

        logger.info(f"任务 {task_id} 已提交到队列")
        return task_id

    async def _execute_spider_task(
        self,
        task_id: str,
        urls: List[str],
        config: Dict[str, Any],
        user_id: int
    ):
        """执行爬虫任务"""
        async with self.semaphore:  # 控制并发任务数
            progress = self.task_progress[task_id]

            try:
                # 更新任务状态
                progress.status = TaskStatus.RUNNING
                progress.start_time = datetime.now()

                # 配置爬虫
                spider_config = SpiderConfig(
                    max_concurrent=config.get('max_concurrent', 10),
                    request_delay=config.get('request_delay', 0.5)
                )

                # 执行爬虫任务
                async with AsyncSpider(spider_config) as spider:
                    results = await spider.crawl_urls(urls)

                # 更新进度
                progress.status = TaskStatus.COMPLETED
                progress.end_time = datetime.now()
                progress.completed_urls = len(results)
                progress.successful_urls = sum(1 for r in results if not r.error)
                progress.failed_urls = sum(1 for r in results if r.error)

                logger.info(f"任务 {task_id} 执行完成")

            except Exception as e:
                progress.status = TaskStatus.FAILED
                progress.end_time = datetime.now()
                logger.error(f"任务 {task_id} 执行失败: {e}")

            finally:
                # 清理任务
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]

    def get_task_progress(self, task_id: str) -> Optional[TaskProgress]:
        """获取任务进度"""
        return self.task_progress.get(task_id)

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.cancel()
            return True
        return False

# 全局任务队列实例
task_queue = AsyncSpiderTaskQueue()

# FastAPI集成
from fastapi import APIRouter, Depends, HTTPException
from app.middleware.auth import get_current_active_user
from app.models.auth import TokenData

spider_router = APIRouter(prefix="/async/spider", tags=["异步爬虫任务"])

@spider_router.post("/tasks")
async def create_spider_task_async(
    task_data: Dict[str, Any],
    current_user: TokenData = Depends(get_current_active_user)
):
    """创建异步爬虫任务"""
    try:
        task_id = await task_queue.submit_task(
            urls=task_data['urls'],
            config=task_data.get('config', {}),
            user_id=current_user.user_id
        )

        return {
            'success': True,
            'message': '异步爬虫任务创建成功',
            'data': {'task_id': task_id}
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@spider_router.get("/tasks/{task_id}/progress")
async def get_task_progress_async(
    task_id: str,
    current_user: TokenData = Depends(get_current_active_user)
):
    """获取任务进度"""
    progress = task_queue.get_task_progress(task_id)

    if not progress:
        raise HTTPException(status_code=404, detail="任务不存在")

    return {
        'success': True,
        'data': {
            'task_id': task_id,
            'status': progress.status.value,
            'total_urls': progress.total_urls,
            'completed_urls': progress.completed_urls,
            'successful_urls': progress.successful_urls,
            'failed_urls': progress.failed_urls,
            'progress_percentage': (progress.completed_urls / max(progress.total_urls, 1)) * 100
        }
    }
```

---

## 📊 第四步：性能测试与对比 (30分钟)

### 综合性能测试
```python
# 文件：tests/performance_test.py
import asyncio
import aiohttp
import requests
import time
import statistics
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
import json

class PerformanceTestSuite:
    """性能测试套件"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = {}

    async def test_async_api_performance(self, endpoint: str, concurrent_requests: int = 50):
        """测试异步API性能"""
        print(f"\n=== 测试异步API性能: {endpoint} ===")

        async with aiohttp.ClientSession() as session:
            # 准备请求
            tasks = []
            start_time = time.time()

            for i in range(concurrent_requests):
                task = self._make_async_request(session, f"{self.base_url}{endpoint}")
                tasks.append(task)

            # 并发执行
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time

            # 分析结果
            successful_requests = sum(1 for r in results if isinstance(r, dict) and r.get('success'))
            failed_requests = len(results) - successful_requests

            response_times = [r.get('response_time', 0) for r in results if isinstance(r, dict)]

            stats = {
                'endpoint': endpoint,
                'type': 'async',
                'concurrent_requests': concurrent_requests,
                'total_time': total_time,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'requests_per_second': concurrent_requests / total_time,
                'avg_response_time': statistics.mean(response_times) if response_times else 0,
                'min_response_time': min(response_times) if response_times else 0,
                'max_response_time': max(response_times) if response_times else 0,
                'median_response_time': statistics.median(response_times) if response_times else 0
            }

            self.test_results[f"async_{endpoint.replace('/', '_')}"] = stats
            self._print_performance_stats(stats)

            return stats

    def test_sync_api_performance(self, endpoint: str, concurrent_requests: int = 50):
        """测试同步API性能"""
        print(f"\n=== 测试同步API性能: {endpoint} ===")

        start_time = time.time()

        # 使用线程池模拟并发
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for i in range(concurrent_requests):
                future = executor.submit(self._make_sync_request, f"{self.base_url}{endpoint}")
                futures.append(future)

            # 等待所有请求完成
            results = [future.result() for future in futures]

        total_time = time.time() - start_time

        # 分析结果
        successful_requests = sum(1 for r in results if r.get('success'))
        failed_requests = len(results) - successful_requests

        response_times = [r.get('response_time', 0) for r in results]

        stats = {
            'endpoint': endpoint,
            'type': 'sync',
            'concurrent_requests': concurrent_requests,
            'total_time': total_time,
            'successful_requests': successful_requests,
            'failed_requests': failed_requests,
            'requests_per_second': concurrent_requests / total_time,
            'avg_response_time': statistics.mean(response_times) if response_times else 0,
            'min_response_time': min(response_times) if response_times else 0,
            'max_response_time': max(response_times) if response_times else 0,
            'median_response_time': statistics.median(response_times) if response_times else 0
        }

        self.test_results[f"sync_{endpoint.replace('/', '_')}"] = stats
        self._print_performance_stats(stats)

        return stats

    async def _make_async_request(self, session: aiohttp.ClientSession, url: str) -> Dict[str, Any]:
        """发送异步请求"""
        try:
            start_time = time.time()
            async with session.get(url) as response:
                await response.text()
                response_time = time.time() - start_time

                return {
                    'success': response.status == 200,
                    'status_code': response.status,
                    'response_time': response_time
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': 0
            }

    def _make_sync_request(self, url: str) -> Dict[str, Any]:
        """发送同步请求"""
        try:
            start_time = time.time()
            response = requests.get(url, timeout=10)
            response_time = time.time() - start_time

            return {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'response_time': response_time
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': 0
            }

    def _print_performance_stats(self, stats: Dict[str, Any]):
        """打印性能统计"""
        print(f"  类型: {stats['type'].upper()}")
        print(f"  并发请求数: {stats['concurrent_requests']}")
        print(f"  总耗时: {stats['total_time']:.3f}s")
        print(f"  成功请求: {stats['successful_requests']}")
        print(f"  失败请求: {stats['failed_requests']}")
        print(f"  QPS: {stats['requests_per_second']:.2f}")
        print(f"  平均响应时间: {stats['avg_response_time']:.3f}s")
        print(f"  最小响应时间: {stats['min_response_time']:.3f}s")
        print(f"  最大响应时间: {stats['max_response_time']:.3f}s")
        print(f"  中位数响应时间: {stats['median_response_time']:.3f}s")

    def compare_performance(self, async_stats: Dict, sync_stats: Dict):
        """对比异步和同步性能"""
        print(f"\n=== 性能对比分析 ===")

        qps_improvement = (async_stats['requests_per_second'] / sync_stats['requests_per_second']) - 1
        response_time_improvement = (sync_stats['avg_response_time'] / async_stats['avg_response_time']) - 1

        print(f"QPS提升: {qps_improvement:.1%}")
        print(f"响应时间改善: {response_time_improvement:.1%}")
        print(f"异步QPS: {async_stats['requests_per_second']:.2f}")
        print(f"同步QPS: {sync_stats['requests_per_second']:.2f}")
        print(f"异步平均响应时间: {async_stats['avg_response_time']:.3f}s")
        print(f"同步平均响应时间: {sync_stats['avg_response_time']:.3f}s")

    def generate_performance_report(self, filename: str = "performance_report.json"):
        """生成性能报告"""
        report = {
            'test_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'results': self.test_results,
            'summary': self._generate_summary()
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"\n性能报告已保存到: {filename}")
        return report

    def _generate_summary(self) -> Dict[str, Any]:
        """生成测试摘要"""
        if not self.test_results:
            return {}

        async_results = [r for r in self.test_results.values() if r['type'] == 'async']
        sync_results = [r for r in self.test_results.values() if r['type'] == 'sync']

        summary = {
            'total_tests': len(self.test_results),
            'async_tests': len(async_results),
            'sync_tests': len(sync_results)
        }

        if async_results:
            summary['async_avg_qps'] = statistics.mean([r['requests_per_second'] for r in async_results])
            summary['async_avg_response_time'] = statistics.mean([r['avg_response_time'] for r in async_results])

        if sync_results:
            summary['sync_avg_qps'] = statistics.mean([r['requests_per_second'] for r in sync_results])
            summary['sync_avg_response_time'] = statistics.mean([r['avg_response_time'] for r in sync_results])

        return summary

# 运行性能测试
async def run_performance_tests():
    """运行完整的性能测试"""
    print("🚀 开始性能测试")
    print("=" * 60)

    tester = PerformanceTestSuite()

    # 测试异步API
    async_stats = await tester.test_async_api_performance("/async/videos/", 50)

    # 测试同步API（如果存在）
    sync_stats = tester.test_sync_api_performance("/api/v1/videos/", 50)

    # 对比性能
    tester.compare_performance(async_stats, sync_stats)

    # 生成报告
    tester.generate_performance_report()

    print("\n✅ 性能测试完成")

if __name__ == "__main__":
    asyncio.run(run_performance_tests())
```

---

## 🔗 第五步：系统整合与部署 (15分钟)

### 主应用整合
```python
# 文件：app/main_async.py (异步版本主应用)
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import time
import asyncio
from contextlib import asynccontextmanager

from app.routers import auth
from app.api.async_videos import router as async_video_router
from app.services.async_spider_service import spider_router
from app.database.async_connection import init_async_db

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    print("🚀 启动异步应用...")
    await init_async_db()
    print("✅ 数据库初始化完成")

    yield

    # 关闭时清理
    print("🛑 关闭异步应用...")

# 创建异步FastAPI应用
app = FastAPI(
    title="高性能异步API系统",
    description="集成JWT认证、异步爬虫、数据库优化的高性能API",
    version="3.0.0",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 性能监控中间件
@app.middleware("http")
async def performance_monitoring_middleware(request: Request, call_next):
    """性能监控中间件"""
    start_time = time.time()

    response = await call_next(request)

    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(round(process_time, 3))
    response.headers["X-API-Version"] = "3.0.0-async"

    # 记录慢请求
    if process_time > 1.0:
        print(f"⚠️ 慢请求: {request.method} {request.url} 耗时 {process_time:.3f}s")

    return response

# 注册路由
app.include_router(auth.router, prefix="/api/v1")
app.include_router(async_video_router)
app.include_router(spider_router)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "高性能异步API系统运行正常",
        "version": "3.0.0",
        "features": [
            "异步数据库操作",
            "异步爬虫任务队列",
            "JWT认证系统",
            "性能监控",
            "并发控制"
        ],
        "performance": {
            "async_support": True,
            "concurrent_requests": "高并发支持",
            "response_time": "低延迟响应"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "async_support": True
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main_async:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        workers=1  # 异步应用通常使用单进程
    )
```

### 部署配置
```yaml
# 文件：docker-compose.async.yml
version: '3.8'

services:
  async-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://user:password@db:5432/async_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    command: uvicorn app.main_async:app --host 0.0.0.0 --port 8000

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: async_db
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

---

## ✅ 晚上学习检查清单

### 异步API开发 (必须掌握)
- [ ] 理解异步API的优势和适用场景
- [ ] 能将同步API重构为异步版本
- [ ] 能实现异步数据库操作和外部API调用
- [ ] 能添加性能监控中间件
- [ ] 能处理异步异常和错误

### 任务队列管理 (核心技能)
- [ ] 能设计异步任务队列系统
- [ ] 能实现任务状态管理和进度跟踪
- [ ] 能控制并发任务数量
- [ ] 能实现任务取消和错误处理
- [ ] 能集成任务队列到API接口

### 性能测试能力 (质量保证)
- [ ] 能编写异步和同步API性能测试
- [ ] 能进行并发压力测试
- [ ] 能分析和对比性能指标
- [ ] 能生成性能测试报告
- [ ] 能识别性能瓶颈和优化点

### 系统整合技能 (企业级)
- [ ] 能整合多个异步组件
- [ ] 能配置应用生命周期管理
- [ ] 能添加系统监控和日志
- [ ] 能进行容器化部署
- [ ] 能优化系统资源使用

---

## 🚀 晚上总结与明日预告

### 🎯 晚上成果
完成了高性能异步API系统的全面构建：
1. ✅ 重构了同步API为高性能异步版本
2. ✅ 实现了异步爬虫任务队列系统
3. ✅ 开发了全面的性能测试套件
4. ✅ 完成了系统整合和部署配置
5. ✅ 建立了性能监控和错误处理机制

### 📊 **8.4全天学习成果总结**
- **上午**: 数据库优化与SQLAlchemy进阶 (3小时)
- **下午**: 异步爬虫技术进阶 (3小时)
- **晚上**: 高性能API开发 (2小时)
- **总计**: 8小时完整学习计划 ✅

### 🌅 明日预告
明天将学习更高级的系统架构：
- 微服务架构设计
- 分布式系统开发
- 容器化和云部署
- 系统监控和运维

### 💡 实践建议
1. **立即验证** (20分钟)：
   - 启动异步API系统
   - 运行性能测试对比
   - 创建异步爬虫任务

2. **深入理解** (15分钟)：
   - 分析异步vs同步性能差异
   - 理解任务队列的工作原理
   - 观察系统资源使用情况

3. **扩展练习** (15分钟)：
   - 优化并发控制策略
   - 添加更多性能监控指标
   - 实现任务结果缓存

### 📈 性能提升总结
通过今天的学习，你应该实现了：
- **API响应速度**: 3-10倍提升
- **并发处理能力**: 10-50倍提升
- **资源利用率**: 显著改善
- **系统稳定性**: 大幅增强

### 🎓 技能等级评估
完成8.4学习后，你已达到：
- 🥇 **数据库优化专家级**
- 🥇 **异步编程高级水平**
- 🥈 **系统架构中级水平**
- 🥉 **性能调优入门水平**

### 🔥 关键技术掌握
1. **数据库技术栈**：
   - SQLAlchemy ORM优化
   - 索引设计和查询优化
   - 连接池配置和管理

2. **异步技术栈**：
   - asyncio事件循环
   - aiohttp异步HTTP
   - 异步数据库操作

3. **系统集成技术**：
   - FastAPI异步框架
   - 任务队列设计
   - 性能监控系统

**恭喜你！今天完成了从数据库优化到异步系统开发的完整技术栈学习！** 🎉

---

## 📚 进阶学习资源

### 📖 深入阅读
- 《高性能MySQL》- 数据库优化圣经
- 《Python异步编程》- 异步技术权威指南
- 《微服务架构设计模式》- 系统架构进阶

### 🎥 视频教程推荐
- "高并发系统设计实战" - 极客时间
- "Python异步编程深度解析" - 慕课网
- "数据库性能调优实战" - B站技术UP主

### 🛠️ 实战项目
- 构建分布式异步爬虫系统
- 开发高并发电商API
- 实现实时数据处理管道

**明天我们将继续探索更高级的系统架构技术！** 🚀✨

---

## 🕷️ 第三步：异步爬虫任务队列 (45分钟)

### 异步任务队列服务
```python
# 文件：app/services/async_spider_service.py
import asyncio
import uuid
import json
import time
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
import logging

from app.database.async_connection import get_async_db
from app.models.async_models import AsyncSpiderTask, AsyncSpiderResult
from async_spider.core.spider import AsyncSpider, SpiderConfig, SpiderResult

logger = logging.getLogger(__name__)

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskProgress:
    """任务进度"""
    task_id: str
    status: TaskStatus
    total_urls: int = 0
    completed_urls: int = 0
    successful_urls: int = 0
    failed_urls: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None

class AsyncSpiderTaskQueue:
    """异步爬虫任务队列"""

    def __init__(self):
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_progress: Dict[str, TaskProgress] = {}
        self.max_concurrent_tasks = 5
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        self.result_callbacks: Dict[str, List[Callable]] = {}

    async def submit_task(
        self,
        task_id: str,
        urls: List[str],
        config: Dict[str, Any],
        user_id: int,
        db: AsyncSession
    ) -> str:
        """提交爬虫任务"""
        try:
            # 创建任务进度跟踪
            progress = TaskProgress(
                task_id=task_id,
                status=TaskStatus.PENDING,
                total_urls=len(urls)
            )
            self.task_progress[task_id] = progress

            # 保存任务到数据库
            db_task = AsyncSpiderTask(
                id=task_id,
                user_id=user_id,
                urls=json.dumps(urls),
                config=json.dumps(config),
                status=TaskStatus.PENDING.value,
                total_urls=len(urls)
            )
            db.add(db_task)
            await db.commit()

            # 创建异步任务
            task = asyncio.create_task(
                self._execute_spider_task(task_id, urls, config, user_id, db)
            )
            self.running_tasks[task_id] = task

            logger.info(f"任务 {task_id} 已提交到队列")
            return task_id

        except Exception as e:
            logger.error(f"提交任务失败: {e}")
            raise

    async def _execute_spider_task(
        self,
        task_id: str,
        urls: List[str],
        config: Dict[str, Any],
        user_id: int,
        db: AsyncSession
    ):
        """执行爬虫任务"""
        async with self.semaphore:  # 控制并发任务数
            progress = self.task_progress[task_id]

            try:
                # 更新任务状态为运行中
                progress.status = TaskStatus.RUNNING
                progress.start_time = datetime.now()

                await self._update_task_in_db(task_id, {
                    'status': TaskStatus.RUNNING.value,
                    'started_at': progress.start_time
                }, db)

                # 配置爬虫
                spider_config = SpiderConfig(
                    max_concurrent=config.get('max_concurrent', 10),
                    request_delay=config.get('request_delay', 0.5),
                    max_retries=config.get('max_retries', 3)
                )

                # 执行爬虫任务
                async with AsyncSpider(spider_config) as spider:
                    results = await spider.crawl_urls(
                        urls,
                        extractor=self._create_progress_callback(task_id)
                    )

                # 处理结果
                await self._process_results(task_id, results, user_id, db)

                # 更新任务状态为完成
                progress.status = TaskStatus.COMPLETED
                progress.end_time = datetime.now()
                progress.completed_urls = len(results)
                progress.successful_urls = sum(1 for r in results if not r.error)
                progress.failed_urls = sum(1 for r in results if r.error)

                await self._update_task_in_db(task_id, {
                    'status': TaskStatus.COMPLETED.value,
                    'completed_at': progress.end_time,
                    'success_count': progress.successful_urls,
                    'failed_count': progress.failed_urls
                }, db)

                # 执行回调
                await self._execute_callbacks(task_id, results)

                logger.info(f"任务 {task_id} 执行完成")

            except asyncio.CancelledError:
                progress.status = TaskStatus.CANCELLED
                await self._update_task_in_db(task_id, {
                    'status': TaskStatus.CANCELLED.value
                }, db)
                logger.info(f"任务 {task_id} 被取消")

            except Exception as e:
                progress.status = TaskStatus.FAILED
                progress.error_message = str(e)
                progress.end_time = datetime.now()

                await self._update_task_in_db(task_id, {
                    'status': TaskStatus.FAILED.value,
                    'completed_at': progress.end_time,
                    'error_message': str(e)
                }, db)

                logger.error(f"任务 {task_id} 执行失败: {e}")

            finally:
                # 清理任务
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]

    def _create_progress_callback(self, task_id: str) -> Callable:
        """创建进度回调函数"""
        async def progress_callback(content: str, url: str) -> Dict[str, Any]:
            # 更新进度
            if task_id in self.task_progress:
                self.task_progress[task_id].completed_urls += 1

            # 简单的数据提取
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')
            title = soup.find('title')

            return {
                'title': title.text.strip() if title else '',
                'url': url,
                'content_length': len(content),
                'extracted_at': datetime.now().isoformat()
            }

        return progress_callback

    async def _process_results(
        self,
        task_id: str,
        results: List[SpiderResult],
        user_id: int,
        db: AsyncSession
    ):
        """处理爬虫结果"""
        try:
            # 批量保存结果到数据库
            db_results = []
            for result in results:
                db_result = AsyncSpiderResult(
                    task_id=task_id,
                    url=result.url,
                    status_code=result.status_code,
                    success=not bool(result.error),
                    response_time=result.response_time,
                    extracted_data=json.dumps(result.extracted_data) if result.extracted_data else None,
                    error_message=result.error
                )
                db_results.append(db_result)

            db.add_all(db_results)
            await db.commit()

            logger.info(f"任务 {task_id} 的 {len(results)} 个结果已保存")

        except Exception as e:
            logger.error(f"保存任务结果失败: {e}")
            await db.rollback()
            raise

    async def _update_task_in_db(
        self,
        task_id: str,
        updates: Dict[str, Any],
        db: AsyncSession
    ):
        """更新数据库中的任务状态"""
        try:
            stmt = update(AsyncSpiderTask).where(
                AsyncSpiderTask.id == task_id
            ).values(**updates)

            await db.execute(stmt)
            await db.commit()

        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            await db.rollback()

    async def _execute_callbacks(self, task_id: str, results: List[SpiderResult]):
        """执行任务完成回调"""
        if task_id in self.result_callbacks:
            for callback in self.result_callbacks[task_id]:
                try:
                    await callback(task_id, results)
                except Exception as e:
                    logger.error(f"执行回调失败: {e}")

    def add_result_callback(self, task_id: str, callback: Callable):
        """添加结果回调"""
        if task_id not in self.result_callbacks:
            self.result_callbacks[task_id] = []
        self.result_callbacks[task_id].append(callback)

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.cancel()
            return True
        return False

    def get_task_progress(self, task_id: str) -> Optional[TaskProgress]:
        """获取任务进度"""
        return self.task_progress.get(task_id)

    def get_all_tasks_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        status = {}
        for task_id, progress in self.task_progress.items():
            status[task_id] = {
                'status': progress.status.value,
                'total_urls': progress.total_urls,
                'completed_urls': progress.completed_urls,
                'successful_urls': progress.successful_urls,
                'failed_urls': progress.failed_urls,
                'start_time': progress.start_time.isoformat() if progress.start_time else None,
                'end_time': progress.end_time.isoformat() if progress.end_time else None,
                'error_message': progress.error_message
            }
        return status

# 全局任务队列实例
task_queue = AsyncSpiderTaskQueue()

# FastAPI集成
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from app.middleware.auth import get_current_active_user
from app.models.auth import TokenData

spider_router = APIRouter(prefix="/async/spider", tags=["异步爬虫任务"])

@spider_router.post("/tasks", response_model=Dict[str, Any])
async def create_spider_task_async(
    task_data: Dict[str, Any],
    db: AsyncSession = Depends(get_async_db),
    current_user: TokenData = Depends(get_current_active_user)
):
    """创建异步爬虫任务"""
    try:
        task_id = str(uuid.uuid4())
        urls = task_data['urls']
        config = task_data.get('config', {})

        await task_queue.submit_task(
            task_id=task_id,
            urls=urls,
            config=config,
            user_id=current_user.user_id,
            db=db
        )

        return {
            'success': True,
            'message': '异步爬虫任务创建成功',
            'data': {
                'task_id': task_id,
                'status': 'pending',
                'total_urls': len(urls)
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"创建任务失败: {str(e)}"
        )

@spider_router.get("/tasks/{task_id}/progress", response_model=Dict[str, Any])
async def get_task_progress_async(
    task_id: str,
    current_user: TokenData = Depends(get_current_active_user)
):
    """获取任务进度"""
    progress = task_queue.get_task_progress(task_id)

    if not progress:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )

    return {
        'success': True,
        'data': {
            'task_id': task_id,
            'status': progress.status.value,
            'total_urls': progress.total_urls,
            'completed_urls': progress.completed_urls,
            'successful_urls': progress.successful_urls,
            'failed_urls': progress.failed_urls,
            'progress_percentage': (progress.completed_urls / max(progress.total_urls, 1)) * 100,
            'start_time': progress.start_time.isoformat() if progress.start_time else None,
            'end_time': progress.end_time.isoformat() if progress.end_time else None,
            'error_message': progress.error_message
        }
    }

@spider_router.delete("/tasks/{task_id}", response_model=Dict[str, Any])
async def cancel_task_async(
    task_id: str,
    current_user: TokenData = Depends(get_current_active_user)
):
    """取消任务"""
    success = await task_queue.cancel_task(task_id)

    if success:
        return {
            'success': True,
            'message': '任务取消成功'
        }
    else:
        return {
            'success': False,
            'message': '任务不存在或已完成'
        }

@spider_router.get("/tasks/status", response_model=Dict[str, Any])
async def get_all_tasks_status_async(
    current_user: TokenData = Depends(get_current_active_user)
):
    """获取所有任务状态"""
    status = task_queue.get_all_tasks_status()

    return {
        'success': True,
        'data': {
            'tasks': status,
            'total_tasks': len(status),
            'running_tasks': len([t for t in status.values() if t['status'] == 'running'])
        }
    }
```
