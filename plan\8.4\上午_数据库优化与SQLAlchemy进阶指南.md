# 🗄️ 数据库优化与SQLAlchemy进阶指南 (上午专用)

## 🎯 学习目标：从基础到企业级数据库优化

### 📚 第一步：理解数据库优化的本质 (20分钟)

**什么是数据库优化？**
- 数据库优化 = 提升数据库查询性能、减少资源消耗的技术手段
- 目标：更快的查询速度、更低的资源占用、更好的用户体验
- 核心：索引设计、查询优化、架构调整

**数据库性能瓶颈分析**：
```python
# 常见性能问题
# 1. 查询慢：缺少索引、复杂查询、全表扫描
# 2. 连接慢：连接池配置不当、网络延迟
# 3. 写入慢：锁竞争、事务设计不当、批量操作缺失
# 4. 存储慢：磁盘I/O瓶颈、数据类型选择不当

# 优化策略层次
# 1. 应用层：查询优化、连接池、缓存
# 2. 数据库层：索引设计、表结构优化
# 3. 系统层：硬件配置、操作系统调优
```

**为什么选择SQLAlchemy？**
- 🚀 **ORM优势**：对象关系映射，代码更清晰
- 🔧 **灵活性**：支持原生SQL和ORM混用
- 📊 **性能工具**：内置查询分析和优化工具
- 🔒 **安全性**：自动防SQL注入

---

## 🎥 第二步：视频学习资源 (60分钟)

### 推荐视频学习路径

#### 核心视频1：数据库索引原理 (20分钟)
**B站推荐**：
- 🎬 "MySQL索引底层原理详解" by 尚硅谷
- 🎬 "B+树索引结构深度解析" by 黑马程序员
- 🎬 "复合索引设计最佳实践" by 动力节点

**学习重点**：
- B+树索引结构和查找过程
- 聚簇索引vs非聚簇索引
- 复合索引的最左前缀原则
- 索引失效的常见场景

#### 核心视频2：SQLAlchemy性能优化 (20分钟)
**B站推荐**：
- 🎬 "SQLAlchemy ORM性能优化实战" by 程序员鱼皮
- 🎬 "Python数据库连接池配置" by 老男孩教育
- 🎬 "N+1查询问题解决方案" by 千锋教育

**学习重点**：
- 懒加载vs急加载策略
- 查询优化和SQL生成
- 连接池配置和管理
- 批量操作最佳实践

#### 核心视频3：数据库设计模式 (20分钟)
**B站推荐**：
- 🎬 "数据库表设计规范与优化" by 咕泡学院
- 🎬 "分库分表设计实战" by 马士兵教育
- 🎬 "数据库事务与锁机制" by 图灵学院

**学习重点**：
- 数据库范式和反范式设计
- 分区表和分表策略
- 事务隔离级别选择
- 读写分离架构

### 视频学习笔记模板
```markdown
## 视频学习笔记 - 数据库优化

### 视频1：MySQL索引底层原理详解
**时长**：XX分钟
**关键概念**：
- [ ] B+树结构特点
- [ ] 索引查找过程
- [ ] 聚簇索引原理
- [ ] 复合索引设计

**重要代码片段**：
```sql
-- 记录视频中的重要SQL示例
```

**疑问和思考**：
- 问题1：
- 问题2：

### 视频2：SQLAlchemy ORM性能优化实战
**时长**：XX分钟
**关键概念**：
- [ ] 懒加载配置
- [ ] 查询优化技巧
- [ ] 连接池设置
- [ ] 批量操作方法

**实践要点**：
- 要点1：
- 要点2：
```

---

## 🛠️ 第三步：环境搭建与依赖安装 (15分钟)

### 安装数据库相关依赖
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows

# 安装核心依赖
pip install sqlalchemy[asyncio]  # SQLAlchemy异步支持
pip install alembic              # 数据库迁移工具
pip install psycopg2-binary      # PostgreSQL驱动
pip install pymysql              # MySQL驱动
pip install aiosqlite            # SQLite异步驱动

# 性能分析工具
pip install sqlalchemy-utils    # SQLAlchemy工具集
pip install sqlparse            # SQL解析器
pip install memory-profiler     # 内存分析
pip install line-profiler       # 代码性能分析

# 验证安装
python -c "import sqlalchemy; print(f'SQLAlchemy版本: {sqlalchemy.__version__}')"
```

### 项目结构设计
```
app/
├── database/
│   ├── __init__.py
│   ├── connection.py       # 数据库连接配置
│   ├── models.py          # 优化后的数据模型
│   ├── migrations/        # 数据库迁移文件
│   └── query_optimizer.py # 查询优化工具
├── performance/
│   ├── __init__.py
│   ├── profiler.py        # 性能分析工具
│   ├── benchmarks.py      # 性能基准测试
│   └── monitoring.py     # 性能监控
└── tests/
    ├── test_performance.py
    └── test_database.py
```

---

## 🔧 第四步：数据库连接优化 (45分钟)

### 高性能数据库连接配置
```python
# 文件：app/database/connection.py
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
import logging
import time
from typing import Generator

# 配置日志
logging.basicConfig()
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        # 数据库URL配置
        self.DATABASE_URL = "sqlite:///./test_optimized.db"
        
        # 连接池配置
        self.POOL_SIZE = 20          # 连接池大小
        self.MAX_OVERFLOW = 30       # 最大溢出连接数
        self.POOL_TIMEOUT = 30       # 获取连接超时时间
        self.POOL_RECYCLE = 3600     # 连接回收时间(秒)
        self.POOL_PRE_PING = True    # 连接前ping检查
        
        # 查询配置
        self.ECHO = True             # 是否打印SQL
        self.ECHO_POOL = False       # 是否打印连接池日志
        
    def get_engine_config(self) -> dict:
        """获取引擎配置"""
        return {
            'url': self.DATABASE_URL,
            'echo': self.ECHO,
            'echo_pool': self.ECHO_POOL,
            'poolclass': QueuePool,
            'pool_size': self.POOL_SIZE,
            'max_overflow': self.MAX_OVERFLOW,
            'pool_timeout': self.POOL_TIMEOUT,
            'pool_recycle': self.POOL_RECYCLE,
            'pool_pre_ping': self.POOL_PRE_PING,
            # SQLite特定配置
            'connect_args': {
                'check_same_thread': False,  # SQLite多线程支持
                'timeout': 20,               # 数据库锁超时
            }
        }

# 全局配置
config = DatabaseConfig()

# 创建引擎
engine = create_engine(**config.get_engine_config())

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False  # 提交后不过期对象
)

# 创建基础模型类
Base = declarative_base()

# 性能监控事件
@event.listens_for(engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """查询执行前的监控"""
    context._query_start_time = time.time()

@event.listens_for(engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """查询执行后的监控"""
    total = time.time() - context._query_start_time
    if total > 0.1:  # 记录超过100ms的慢查询
        logging.warning(f"慢查询检测 ({total:.3f}s): {statement[:100]}...")

def get_db() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db_stats() -> dict:
    """获取数据库连接统计"""
    pool = engine.pool
    return {
        "pool_size": pool.size(),
        "checked_in": pool.checkedin(),
        "checked_out": pool.checkedout(),
        "overflow": pool.overflow(),
        "invalid": pool.invalid()
    }

# 测试连接
if __name__ == "__main__":
    print("=== 数据库连接测试 ===")
    
    # 测试连接
    try:
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            print("✅ 数据库连接成功")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
    
    # 显示连接池状态
    stats = get_db_stats()
    print(f"连接池状态: {stats}")
```

---

## 📊 第五步：优化数据模型设计 (60分钟)

### 高性能数据模型
```python
# 文件：app/database/models.py
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Index
from sqlalchemy import event, func, text
from sqlalchemy.orm import relationship, validates, declarative_base
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.hybrid import hybrid_property
from datetime import datetime
import uuid

Base = declarative_base()

class TimestampMixin:
    """时间戳混入类"""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

class User(Base, TimestampMixin):
    """优化后的用户模型"""
    __tablename__ = 'users'

    # 主键使用自增整数（比UUID性能更好）
    id = Column(Integer, primary_key=True, autoincrement=True)

    # 用户名：唯一索引，长度限制
    username = Column(String(50), unique=True, nullable=False, index=True)

    # 邮箱：唯一索引，长度限制
    email = Column(String(255), unique=True, nullable=False, index=True)

    # 密码哈希：不需要索引
    password_hash = Column(String(255), nullable=False)

    # 用户状态：枚举类型，添加索引用于筛选
    is_active = Column(Boolean, default=True, nullable=False, index=True)

    # 最后登录时间：用于用户活跃度分析
    last_login = Column(DateTime, index=True)

    # 关系定义（懒加载优化）
    videos = relationship("Video", back_populates="user", lazy="select")
    spider_tasks = relationship("SpiderTask", back_populates="user", lazy="select")

    # 复合索引：常用查询组合
    __table_args__ = (
        Index('idx_user_active_created', 'is_active', 'created_at'),
        Index('idx_user_email_active', 'email', 'is_active'),
    )

    @hybrid_property
    def is_recent_user(self):
        """是否为近期注册用户（30天内）"""
        from datetime import timedelta
        return self.created_at > datetime.utcnow() - timedelta(days=30)

    @validates('email')
    def validate_email(self, key, email):
        """邮箱格式验证"""
        import re
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            raise ValueError('Invalid email format')
        return email.lower()

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"

class Video(Base, TimestampMixin):
    """优化后的视频模型"""
    __tablename__ = 'videos'

    id = Column(Integer, primary_key=True, autoincrement=True)

    # 标题：全文索引（如果数据库支持）
    title = Column(String(255), nullable=False, index=True)

    # 描述：使用TEXT类型，不添加索引
    description = Column(Text)

    # URL：唯一索引
    url = Column(String(500), unique=True, nullable=False)

    # 时长（秒）：范围查询索引
    duration = Column(Integer, index=True)

    # 观看次数：用于排序
    view_count = Column(Integer, default=0, nullable=False, index=True)

    # 点赞数：用于排序
    like_count = Column(Integer, default=0, nullable=False, index=True)

    # 状态：枚举索引
    status = Column(String(20), default='active', nullable=False, index=True)

    # 外键：用户ID
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)

    # 关系定义
    user = relationship("User", back_populates="videos")
    tags = relationship("VideoTag", back_populates="video", lazy="select")

    # 复合索引：常用查询组合
    __table_args__ = (
        Index('idx_video_user_status', 'user_id', 'status'),
        Index('idx_video_status_created', 'status', 'created_at'),
        Index('idx_video_popular', 'view_count', 'like_count'),  # 热门视频查询
        Index('idx_video_duration_range', 'duration', 'status'),  # 时长筛选
    )

    @hybrid_property
    def popularity_score(self):
        """人气分数计算"""
        return (self.view_count * 0.7) + (self.like_count * 0.3)

    def increment_view_count(self):
        """增加观看次数（原子操作）"""
        self.view_count = Video.view_count + 1

class VideoTag(Base):
    """视频标签关联表（多对多优化）"""
    __tablename__ = 'video_tags'

    id = Column(Integer, primary_key=True, autoincrement=True)
    video_id = Column(Integer, ForeignKey('videos.id'), nullable=False)
    tag_name = Column(String(50), nullable=False)

    # 关系定义
    video = relationship("Video", back_populates="tags")

    # 复合索引：防重复，优化查询
    __table_args__ = (
        Index('idx_video_tag_unique', 'video_id', 'tag_name', unique=True),
        Index('idx_tag_name', 'tag_name'),  # 按标签查询
    )

class SpiderTask(Base, TimestampMixin):
    """爬虫任务模型"""
    __tablename__ = 'spider_tasks'

    id = Column(Integer, primary_key=True, autoincrement=True)

    # 任务名称
    name = Column(String(100), nullable=False, index=True)

    # 任务状态：枚举索引
    status = Column(String(20), default='pending', nullable=False, index=True)

    # URL列表：JSON存储
    urls = Column(Text, nullable=False)  # 存储JSON字符串

    # 配置信息：JSON存储
    config = Column(Text)  # 存储JSON字符串

    # 结果统计
    total_urls = Column(Integer, default=0, nullable=False)
    success_count = Column(Integer, default=0, nullable=False)
    failed_count = Column(Integer, default=0, nullable=False)

    # 时间记录
    started_at = Column(DateTime, index=True)
    completed_at = Column(DateTime, index=True)

    # 外键：用户ID
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)

    # 关系定义
    user = relationship("User", back_populates="spider_tasks")
    results = relationship("SpiderResult", back_populates="task", lazy="select")

    # 复合索引
    __table_args__ = (
        Index('idx_task_user_status', 'user_id', 'status'),
        Index('idx_task_status_created', 'status', 'created_at'),
        Index('idx_task_completed', 'completed_at', 'status'),
    )

    @hybrid_property
    def success_rate(self):
        """成功率计算"""
        if self.total_urls == 0:
            return 0.0
        return self.success_count / self.total_urls

    @hybrid_property
    def duration_seconds(self):
        """任务执行时长（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

class SpiderResult(Base):
    """爬虫结果模型"""
    __tablename__ = 'spider_results'

    id = Column(Integer, primary_key=True, autoincrement=True)

    # 任务ID
    task_id = Column(Integer, ForeignKey('spider_tasks.id'), nullable=False, index=True)

    # 目标URL
    url = Column(String(500), nullable=False, index=True)

    # 状态码
    status_code = Column(Integer, index=True)

    # 是否成功
    success = Column(Boolean, nullable=False, index=True)

    # 响应时间（毫秒）
    response_time = Column(Integer)

    # 提取的数据：JSON存储
    extracted_data = Column(Text)  # 存储JSON字符串

    # 错误信息
    error_message = Column(Text)

    # 创建时间
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    # 关系定义
    task = relationship("SpiderTask", back_populates="results")

    # 复合索引
    __table_args__ = (
        Index('idx_result_task_success', 'task_id', 'success'),
        Index('idx_result_task_created', 'task_id', 'created_at'),
        Index('idx_result_url_status', 'url', 'status_code'),
    )

# 事件监听器：自动更新统计信息
@event.listens_for(SpiderResult, 'after_insert')
def update_task_stats(mapper, connection, target):
    """插入结果后自动更新任务统计"""
    # 这里可以添加自动更新任务统计的逻辑
    pass

# 创建所有表
def create_tables(engine):
    """创建所有数据表"""
    Base.metadata.create_all(bind=engine)
    print("✅ 数据表创建完成")

# 删除所有表
def drop_tables(engine):
    """删除所有数据表"""
    Base.metadata.drop_all(bind=engine)
    print("✅ 数据表删除完成")

if __name__ == "__main__":
    from app.database.connection import engine

    print("=== 数据模型测试 ===")

    # 创建表
    create_tables(engine)

    # 显示表结构
    for table_name, table in Base.metadata.tables.items():
        print(f"\n表名: {table_name}")
        print("字段:")
        for column in table.columns:
            print(f"  - {column.name}: {column.type}")
        print("索引:")
        for index in table.indexes:
            print(f"  - {index.name}: {[col.name for col in index.columns]}")
```

---

## ⚡ 第六步：查询优化技术 (75分钟)

### 高性能查询优化器
```python
# 文件：app/database/query_optimizer.py
from sqlalchemy.orm import Session, joinedload, selectinload, contains_eager
from sqlalchemy import func, and_, or_, text, select
from sqlalchemy.sql import sqltypes
from typing import List, Dict, Any, Optional, Tuple
import time
import logging
from contextlib import contextmanager

from app.database.models import User, Video, SpiderTask, SpiderResult
from app.database.connection import get_db

logger = logging.getLogger(__name__)

class QueryOptimizer:
    """查询优化器"""

    def __init__(self, db: Session):
        self.db = db
        self.query_cache = {}  # 简单查询缓存

    @contextmanager
    def query_timer(self, query_name: str):
        """查询计时器"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            logger.info(f"查询 '{query_name}' 耗时: {duration:.3f}s")

    def get_users_with_videos_optimized(self, page: int = 1, size: int = 10) -> List[User]:
        """优化的用户视频查询（解决N+1问题）"""
        with self.query_timer("get_users_with_videos"):
            # 使用joinedload一次性加载关联数据
            offset = (page - 1) * size

            users = (
                self.db.query(User)
                .options(joinedload(User.videos))  # 急加载视频数据
                .filter(User.is_active == True)
                .order_by(User.created_at.desc())
                .offset(offset)
                .limit(size)
                .all()
            )

            return users

    def get_popular_videos_optimized(self, limit: int = 20) -> List[Video]:
        """优化的热门视频查询"""
        with self.query_timer("get_popular_videos"):
            # 使用复合索引和计算字段
            videos = (
                self.db.query(Video)
                .options(selectinload(Video.user))  # 选择性加载用户信息
                .filter(Video.status == 'active')
                .order_by(
                    (Video.view_count * 0.7 + Video.like_count * 0.3).desc(),
                    Video.created_at.desc()
                )
                .limit(limit)
                .all()
            )

            return videos

    def search_videos_optimized(self, keyword: str, page: int = 1, size: int = 10) -> Tuple[List[Video], int]:
        """优化的视频搜索（全文搜索）"""
        with self.query_timer("search_videos"):
            offset = (page - 1) * size

            # 基础查询
            base_query = (
                self.db.query(Video)
                .filter(
                    and_(
                        Video.status == 'active',
                        or_(
                            Video.title.ilike(f'%{keyword}%'),
                            Video.description.ilike(f'%{keyword}%')
                        )
                    )
                )
            )

            # 获取总数（优化：只查询count）
            total = base_query.count()

            # 获取分页数据
            videos = (
                base_query
                .options(selectinload(Video.user))
                .order_by(Video.view_count.desc(), Video.created_at.desc())
                .offset(offset)
                .limit(size)
                .all()
            )

            return videos, total

    def get_user_statistics_optimized(self, user_id: int) -> Dict[str, Any]:
        """优化的用户统计查询（聚合查询）"""
        with self.query_timer("get_user_statistics"):
            # 使用单个查询获取所有统计信息
            stats = (
                self.db.query(
                    func.count(Video.id).label('total_videos'),
                    func.sum(Video.view_count).label('total_views'),
                    func.sum(Video.like_count).label('total_likes'),
                    func.avg(Video.view_count).label('avg_views'),
                    func.max(Video.created_at).label('last_video_date')
                )
                .filter(
                    and_(
                        Video.user_id == user_id,
                        Video.status == 'active'
                    )
                )
                .first()
            )

            # 爬虫任务统计
            task_stats = (
                self.db.query(
                    func.count(SpiderTask.id).label('total_tasks'),
                    func.sum(
                        func.case(
                            (SpiderTask.status == 'completed', 1),
                            else_=0
                        )
                    ).label('completed_tasks'),
                    func.avg(SpiderTask.success_count).label('avg_success_rate')
                )
                .filter(SpiderTask.user_id == user_id)
                .first()
            )

            return {
                'video_stats': {
                    'total_videos': stats.total_videos or 0,
                    'total_views': stats.total_views or 0,
                    'total_likes': stats.total_likes or 0,
                    'avg_views': float(stats.avg_views or 0),
                    'last_video_date': stats.last_video_date
                },
                'task_stats': {
                    'total_tasks': task_stats.total_tasks or 0,
                    'completed_tasks': task_stats.completed_tasks or 0,
                    'avg_success_rate': float(task_stats.avg_success_rate or 0)
                }
            }

    def bulk_insert_videos(self, video_data_list: List[Dict[str, Any]]) -> int:
        """批量插入视频（性能优化）"""
        with self.query_timer("bulk_insert_videos"):
            try:
                # 使用bulk_insert_mappings进行批量插入
                self.db.bulk_insert_mappings(Video, video_data_list)
                self.db.commit()
                return len(video_data_list)
            except Exception as e:
                self.db.rollback()
                logger.error(f"批量插入失败: {e}")
                raise

    def bulk_update_view_counts(self, video_updates: List[Dict[str, Any]]) -> int:
        """批量更新观看次数"""
        with self.query_timer("bulk_update_view_counts"):
            try:
                # 使用bulk_update_mappings进行批量更新
                self.db.bulk_update_mappings(Video, video_updates)
                self.db.commit()
                return len(video_updates)
            except Exception as e:
                self.db.rollback()
                logger.error(f"批量更新失败: {e}")
                raise

    def get_trending_videos_with_cache(self, hours: int = 24) -> List[Video]:
        """带缓存的趋势视频查询"""
        cache_key = f"trending_videos_{hours}h"

        # 检查缓存
        if cache_key in self.query_cache:
            cached_time, cached_data = self.query_cache[cache_key]
            if time.time() - cached_time < 300:  # 5分钟缓存
                logger.info(f"使用缓存数据: {cache_key}")
                return cached_data

        with self.query_timer("get_trending_videos"):
            # 计算时间范围
            from datetime import datetime, timedelta
            since_time = datetime.utcnow() - timedelta(hours=hours)

            videos = (
                self.db.query(Video)
                .filter(
                    and_(
                        Video.status == 'active',
                        Video.created_at >= since_time
                    )
                )
                .order_by(
                    (Video.view_count + Video.like_count * 2).desc()
                )
                .limit(50)
                .all()
            )

            # 更新缓存
            self.query_cache[cache_key] = (time.time(), videos)
            return videos

    def explain_query(self, query) -> str:
        """分析查询执行计划"""
        try:
            # 获取查询的SQL语句
            sql_query = str(query.statement.compile(compile_kwargs={"literal_binds": True}))

            # 执行EXPLAIN（SQLite示例）
            explain_result = self.db.execute(text(f"EXPLAIN QUERY PLAN {sql_query}"))

            plan_lines = []
            for row in explain_result:
                plan_lines.append(str(row))

            return "\n".join(plan_lines)
        except Exception as e:
            return f"无法分析查询计划: {e}"

    def clear_cache(self):
        """清除查询缓存"""
        self.query_cache.clear()
        logger.info("查询缓存已清除")

# 性能基准测试
class PerformanceBenchmark:
    """性能基准测试"""

    def __init__(self, db: Session):
        self.db = db
        self.optimizer = QueryOptimizer(db)

    def benchmark_query_methods(self, user_count: int = 100):
        """对比不同查询方法的性能"""
        print("=== 查询性能基准测试 ===")

        # 测试1：N+1查询问题对比
        print("\n1. N+1查询问题对比")

        # 低效方法：N+1查询
        start_time = time.time()
        users_inefficient = self.db.query(User).limit(10).all()
        for user in users_inefficient:
            videos = user.videos  # 这会触发额外查询
        inefficient_time = time.time() - start_time

        # 高效方法：joinedload
        start_time = time.time()
        users_efficient = self.optimizer.get_users_with_videos_optimized(size=10)
        efficient_time = time.time() - start_time

        print(f"  低效方法耗时: {inefficient_time:.3f}s")
        print(f"  高效方法耗时: {efficient_time:.3f}s")
        print(f"  性能提升: {inefficient_time/efficient_time:.1f}x")

        # 测试2：聚合查询性能
        print("\n2. 聚合查询性能测试")

        start_time = time.time()
        stats = self.optimizer.get_user_statistics_optimized(1)
        aggregation_time = time.time() - start_time

        print(f"  聚合查询耗时: {aggregation_time:.3f}s")
        print(f"  统计结果: {stats}")

        # 测试3：批量操作性能
        print("\n3. 批量操作性能测试")

        # 准备测试数据
        test_videos = [
            {
                'title': f'测试视频 {i}',
                'url': f'http://example.com/video/{i}',
                'user_id': 1,
                'view_count': i * 10,
                'like_count': i * 2
            }
            for i in range(100)
        ]

        # 单条插入
        start_time = time.time()
        for video_data in test_videos[:10]:  # 只测试10条
            video = Video(**video_data)
            self.db.add(video)
        self.db.commit()
        single_insert_time = time.time() - start_time

        # 批量插入
        start_time = time.time()
        inserted_count = self.optimizer.bulk_insert_videos(test_videos[10:20])
        bulk_insert_time = time.time() - start_time

        print(f"  单条插入10条耗时: {single_insert_time:.3f}s")
        print(f"  批量插入10条耗时: {bulk_insert_time:.3f}s")
        print(f"  批量插入性能提升: {single_insert_time/bulk_insert_time:.1f}x")

# 测试查询优化器
if __name__ == "__main__":
    from app.database.connection import engine, SessionLocal
    from app.database.models import create_tables

    # 创建表
    create_tables(engine)

    # 创建测试会话
    db = SessionLocal()

    try:
        # 创建优化器
        optimizer = QueryOptimizer(db)

        # 运行基准测试
        benchmark = PerformanceBenchmark(db)
        benchmark.benchmark_query_methods()

    finally:
        db.close()
```

---

## ✅ 上午学习检查清单

### 理论知识掌握 (必须理解)
- [ ] 理解数据库性能瓶颈的根本原因
- [ ] 掌握B+树索引的工作原理和设计原则
- [ ] 理解复合索引的最左前缀原则
- [ ] 知道聚簇索引和非聚簇索引的区别
- [ ] 理解数据库连接池的配置和优化

### SQLAlchemy技能 (必须会做)
- [ ] 能配置高性能的数据库连接池
- [ ] 能设计优化的数据模型和索引
- [ ] 能使用joinedload和selectinload解决N+1问题
- [ ] 能编写高效的聚合查询和批量操作
- [ ] 能使用事件监听器进行性能监控

### 查询优化技术 (核心能力)
- [ ] 能识别和解决慢查询问题
- [ ] 能使用EXPLAIN分析查询执行计划
- [ ] 能实现查询缓存机制
- [ ] 能进行批量插入和更新优化
- [ ] 能设计高效的分页查询

### 性能测试能力 (质量保证)
- [ ] 能编写性能基准测试
- [ ] 能对比不同查询方法的性能
- [ ] 能监控数据库连接池状态
- [ ] 能分析查询性能瓶颈
- [ ] 能生成性能优化报告

---

## 🚀 上午总结与下午预告

### 🎯 上午成果
完成了企业级数据库优化的核心技能：
1. ✅ 深入理解了数据库性能优化原理
2. ✅ 掌握了SQLAlchemy高级配置和优化技术
3. ✅ 实现了高性能数据模型设计
4. ✅ 开发了查询优化器和性能监控工具
5. ✅ 学会了批量操作和缓存策略
6. ✅ 编写了完整的性能基准测试

### 🌞 下午预告
下午将学习异步爬虫技术：
- asyncio异步编程基础
- aiohttp异步HTTP请求
- 异步爬虫架构设计
- 并发控制和性能优化
- 异步数据库操作

### 💡 实践建议
1. **立即实践** (20分钟)：
   - 运行数据库连接测试
   - 创建优化的数据模型
   - 执行性能基准测试

2. **深入理解** (15分钟)：
   - 观看推荐的视频教程
   - 分析查询执行计划
   - 测试不同索引策略的效果

3. **扩展练习** (15分钟)：
   - 尝试不同的连接池配置
   - 实现更复杂的聚合查询
   - 添加更多性能监控指标

### 📊 性能优化效果预期
通过今天上午的学习，你应该能够实现：
- **查询性能提升**: 3-10倍
- **批量操作优化**: 5-20倍
- **内存使用优化**: 30-50%
- **连接池效率**: 显著提升

### 🎓 技能等级评估
完成上午学习后，你将达到：
- 🔰 **初级**: 理解基础优化概念
- 🥉 **中级**: 能进行常见优化操作
- 🥈 **高级**: 能设计高性能数据库架构
- 🥇 **专家**: 能解决复杂性能问题

**记住**：数据库优化是一个持续的过程，需要根据实际业务场景不断调整和改进！

---

## 📚 推荐学习资源

### 📖 深入阅读
- 《高性能MySQL》- 数据库优化圣经
- 《SQLAlchemy官方文档》- 权威技术文档
- 《数据库系统概念》- 理论基础

### 🎥 视频教程补充
- "MySQL性能优化实战课程" - 极客时间
- "Python数据库编程进阶" - 慕课网
- "SQLAlchemy深度解析" - B站技术UP主

### 🛠️ 实践项目
- 优化现有项目的数据库查询
- 实现分布式数据库架构
- 开发数据库性能监控系统

**准备好迎接下午的异步编程挑战了吗？** ⚡
